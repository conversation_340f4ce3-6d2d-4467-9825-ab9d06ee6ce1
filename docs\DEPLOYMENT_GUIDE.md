# AI Integration Service - Deployment Guide

This guide provides comprehensive instructions for deploying the AI Integration Service to various environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Local Development](#local-development)
4. [Docker Deployment](#docker-deployment)
5. [Azure Deployment](#azure-deployment)
6. [AWS Deployment](#aws-deployment)
7. [Kubernetes Deployment](#kubernetes-deployment)
8. [Monitoring and Logging](#monitoring-and-logging)
9. [Security Considerations](#security-considerations)
10. [Troubleshooting](#troubleshooting)

## Prerequisites

- .NET 9.0 SDK
- Docker (for containerized deployment)
- AI Provider API Keys (OpenAI, Claude, Google, etc.)
- SSL Certificate (for production)

## Environment Configuration

### Required Environment Variables

```bash
# AI Provider Configuration
AISettings__OpenAI__ApiKey=your-openai-api-key
AISettings__Claude__ApiKey=your-claude-api-key
AISettings__Google__ApiKey=your-google-api-key

# Authentication
Authentication__ApiKeys__0=your-primary-api-key
Authentication__ApiKeys__1=your-secondary-api-key

# Rate Limiting
RateLimit__AI__RequestsPerMinute=60
RateLimit__AI__RequestsPerHour=1000

# Logging
Serilog__MinimumLevel__Default=Information
Serilog__WriteTo__0__Name=Console
Serilog__WriteTo__1__Name=File
Serilog__WriteTo__1__Args__path=logs/ai-integration-.txt
```

### Configuration Files

#### appsettings.Production.json
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "System": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Authentication": {
    "ApiKeys": ["${Authentication__ApiKeys__0}", "${Authentication__ApiKeys__1}"]
  },
  "AISettings": {
    "DefaultProvider": "OpenAI",
    "OpenAI": {
      "ApiKey": "${AISettings__OpenAI__ApiKey}",
      "Enabled": true
    },
    "Claude": {
      "ApiKey": "${AISettings__Claude__ApiKey}",
      "Enabled": true
    },
    "HttpClient": {
      "TimeoutSeconds": 60,
      "RetryCount": 5,
      "EnableCircuitBreaker": true
    }
  },
  "RateLimit": {
    "AI": {
      "RequestsPerMinute": 100,
      "RequestsPerHour": 2000
    }
  }
}
```

## Local Development

### 1. Clone and Setup
```bash
git clone <repository-url>
cd Ai.Integration.Service
dotnet restore
```

### 2. Configure Development Settings
```bash
# Copy example configuration
cp src/Ai.Integrate.Api/appsettings.Development.json.example src/Ai.Integrate.Api/appsettings.Development.json

# Edit with your API keys
nano src/Ai.Integrate.Api/appsettings.Development.json
```

### 3. Run the Application
```bash
dotnet run --project src/Ai.Integrate.Api
```

### 4. Access the API
- API Documentation: https://localhost:7289/scalar/v1
- Health Check: https://localhost:7289/health
- Providers: https://localhost:7289/api/providers

## Docker Deployment

### 1. Create Dockerfile
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["src/Ai.Integrate.Api/Ai.Integrate.Api.csproj", "src/Ai.Integrate.Api/"]
COPY ["src/Ai.Integrate.Application/Ai.Integrate.Application.csproj", "src/Ai.Integrate.Application/"]
COPY ["src/Ai.Integrate.Infrastructure/Ai.Integrate.Infrastructure.csproj", "src/Ai.Integrate.Infrastructure/"]
COPY ["src/Ai.Integrate.Shared/Ai.Integrate.Shared.csproj", "src/Ai.Integrate.Shared/"]
RUN dotnet restore "src/Ai.Integrate.Api/Ai.Integrate.Api.csproj"
COPY . .
WORKDIR "/src/src/Ai.Integrate.Api"
RUN dotnet build "Ai.Integrate.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Ai.Integrate.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Ai.Integrate.Api.dll"]
```

### 2. Build and Run
```bash
# Build image
docker build -t ai-integration-service .

# Run container
docker run -d \
  --name ai-integration \
  -p 8080:8080 \
  -e AISettings__OpenAI__ApiKey=your-key \
  -e Authentication__ApiKeys__0=your-api-key \
  ai-integration-service
```

### 3. Docker Compose
```yaml
version: '3.8'
services:
  ai-integration:
    build: .
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - AISettings__OpenAI__ApiKey=${OPENAI_API_KEY}
      - Authentication__ApiKeys__0=${API_KEY}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## Azure Deployment

### 1. Azure Container Apps
```bash
# Create resource group
az group create --name ai-integration-rg --location eastus

# Create container app environment
az containerapp env create \
  --name ai-integration-env \
  --resource-group ai-integration-rg \
  --location eastus

# Deploy container app
az containerapp create \
  --name ai-integration-app \
  --resource-group ai-integration-rg \
  --environment ai-integration-env \
  --image your-registry/ai-integration-service:latest \
  --target-port 8080 \
  --ingress external \
  --env-vars \
    ASPNETCORE_ENVIRONMENT=Production \
    AISettings__OpenAI__ApiKey=secretref:openai-key \
  --secrets \
    openai-key=your-openai-api-key
```

### 2. Azure App Service
```bash
# Create App Service plan
az appservice plan create \
  --name ai-integration-plan \
  --resource-group ai-integration-rg \
  --sku B1 \
  --is-linux

# Create web app
az webapp create \
  --resource-group ai-integration-rg \
  --plan ai-integration-plan \
  --name ai-integration-webapp \
  --deployment-container-image-name your-registry/ai-integration-service:latest

# Configure app settings
az webapp config appsettings set \
  --resource-group ai-integration-rg \
  --name ai-integration-webapp \
  --settings \
    ASPNETCORE_ENVIRONMENT=Production \
    AISettings__OpenAI__ApiKey=your-openai-key
```

## AWS Deployment

### 1. AWS ECS Fargate
```json
{
  "family": "ai-integration-service",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "ai-integration",
      "image": "your-registry/ai-integration-service:latest",
      "portMappings": [
        {
          "containerPort": 8080,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ASPNETCORE_ENVIRONMENT",
          "value": "Production"
        }
      ],
      "secrets": [
        {
          "name": "AISettings__OpenAI__ApiKey",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:openai-key"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/ai-integration",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### 2. AWS Lambda (Serverless)
```yaml
# serverless.yml
service: ai-integration-service

provider:
  name: aws
  runtime: dotnet9
  region: us-east-1
  environment:
    ASPNETCORE_ENVIRONMENT: Production
    AISettings__OpenAI__ApiKey: ${ssm:/ai-integration/openai-key}

functions:
  api:
    handler: Ai.Integrate.Api::Ai.Integrate.Api.LambdaEntryPoint::FunctionHandlerAsync
    events:
      - http:
          path: /{proxy+}
          method: ANY
    timeout: 30
```

## Kubernetes Deployment

### 1. Deployment Manifest
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-integration-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-integration-service
  template:
    metadata:
      labels:
        app: ai-integration-service
    spec:
      containers:
      - name: ai-integration
        image: your-registry/ai-integration-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: AISettings__OpenAI__ApiKey
          valueFrom:
            secretKeyRef:
              name: ai-secrets
              key: openai-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: ai-integration-service
spec:
  selector:
    app: ai-integration-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

### 2. Secrets Management
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: ai-secrets
type: Opaque
data:
  openai-key: <base64-encoded-key>
  api-key: <base64-encoded-api-key>
```

## Monitoring and Logging

### 1. Application Insights (Azure)
```json
{
  "ApplicationInsights": {
    "ConnectionString": "InstrumentationKey=your-key"
  }
}
```

### 2. CloudWatch (AWS)
```csharp
// Add to Program.cs
builder.Services.AddAWSService<IAmazonCloudWatchLogs>();
builder.Logging.AddAWSProvider();
```

### 3. Prometheus Metrics
```csharp
// Add to Program.cs
builder.Services.AddPrometheusMetrics();
app.UsePrometheusMetrics();
```

## Security Considerations

### 1. API Key Management
- Use environment variables or secret management services
- Rotate API keys regularly
- Implement key-specific rate limiting

### 2. HTTPS Configuration
```csharp
// Add to Program.cs for production
if (!app.Environment.IsDevelopment())
{
    app.UseHsts();
    app.UseHttpsRedirection();
}
```

### 3. CORS Configuration
```csharp
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.WithOrigins("https://yourdomain.com")
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});
```

## Troubleshooting

### Common Issues

1. **API Key Authentication Failures**
   - Check environment variables
   - Verify API key format
   - Check rate limits

2. **Provider Unavailability**
   - Check network connectivity
   - Verify provider API status
   - Review circuit breaker settings

3. **High Memory Usage**
   - Monitor cache usage
   - Check for memory leaks
   - Adjust container limits

### Debugging Commands

```bash
# Check container logs
docker logs ai-integration

# Check Kubernetes pod logs
kubectl logs -f deployment/ai-integration-service

# Health check
curl -H "X-API-Key: your-key" https://your-domain/api/health
```

### Performance Tuning

1. **Adjust Rate Limits**
2. **Configure Circuit Breaker Settings**
3. **Optimize Cache Expiration**
4. **Scale Horizontally**

This deployment guide provides comprehensive instructions for deploying the AI Integration Service across various platforms and environments.
