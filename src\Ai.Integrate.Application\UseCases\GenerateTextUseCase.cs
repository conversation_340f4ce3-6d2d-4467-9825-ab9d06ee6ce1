using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace Ai.Integrate.Application.UseCases;

/// <summary>
/// Use case for generating text using AI providers
/// </summary>
public class GenerateTextUseCase : IGenerateTextUseCase
{
    private readonly IEnumerable<IAIService> _aiServices;
    private readonly ILogger<GenerateTextUseCase> _logger;

    public GenerateTextUseCase(
        IEnumerable<IAIService> aiServices,
        ILogger<GenerateTextUseCase> logger)
    {
        _aiServices = aiServices ?? throw new ArgumentNullException(nameof(aiServices));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Execute the generate text use case
    /// </summary>
    /// <param name="request">The generation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The generation response</returns>
    public async Task<GenerateResponse> ExecuteAsync(GenerateRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting text generation with provider: {Provider}", request.Provider ?? "default");
        
        try
        {
            var aiService = GetAIService(request.Provider);
            
            if (aiService == null)
            {
                _logger.LogError("No AI service found for provider: {Provider}", request.Provider ?? "default");
                return new GenerateResponse
                {
                    Success = false,
                    ErrorMessage = $"No AI service found for provider: {request.Provider ?? "default"}"
                };
            }

            // Check if the service is available
            var isAvailable = await aiService.IsAvailableAsync(cancellationToken);
            if (!isAvailable)
            {
                _logger.LogWarning("AI service {Provider} is not available", aiService.ProviderName);
                return new GenerateResponse
                {
                    Success = false,
                    ErrorMessage = $"AI service {aiService.ProviderName} is not available"
                };
            }

            var startTime = DateTime.UtcNow;
            var response = await aiService.GenerateTextAsync(request, cancellationToken);
            var endTime = DateTime.UtcNow;
            
            response.Duration = endTime - startTime;
            response.Timestamp = endTime;
            
            _logger.LogInformation("Text generation completed successfully in {Duration}ms", 
                response.Duration.TotalMilliseconds);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during text generation");
            return new GenerateResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred during text generation: {ex.Message}"
            };
        }
    }

    private IAIService? GetAIService(string? providerName)
    {
        if (string.IsNullOrEmpty(providerName))
        {
            // Return the first available service as default
            return _aiServices.FirstOrDefault();
        }

        return _aiServices.FirstOrDefault(s => 
            string.Equals(s.ProviderName, providerName, StringComparison.OrdinalIgnoreCase));
    }
}
