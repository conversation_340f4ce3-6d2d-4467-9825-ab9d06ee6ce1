using System.ComponentModel.DataAnnotations;

namespace Ai.Integrate.Application.Dtos;

/// <summary>
/// Text completion request model
/// </summary>
public class CompletionRequest
{
    /// <summary>
    /// The text prompt to complete
    /// </summary>
    [Required(ErrorMessage = "Prompt is required")]
    [StringLength(10000, ErrorMessage = "Prompt cannot exceed 10000 characters")]
    public string Prompt { get; set; } = string.Empty;
    
    /// <summary>
    /// Maximum number of tokens to generate
    /// </summary>
    [Range(1, 4000, ErrorMessage = "MaxTokens must be between 1 and 4000")]
    public int MaxTokens { get; set; } = 1000;
    
    /// <summary>
    /// Temperature for randomness (0.0 to 2.0)
    /// </summary>
    [Range(0.0, 2.0, ErrorMessage = "Temperature must be between 0.0 and 2.0")]
    public double Temperature { get; set; } = 0.7;
    
    /// <summary>
    /// Top-p sampling parameter
    /// </summary>
    [Range(0.0, 1.0, ErrorMessage = "TopP must be between 0.0 and 1.0")]
    public double TopP { get; set; } = 1.0;
    
    /// <summary>
    /// AI provider to use (optional, will use default if not specified)
    /// </summary>
    public string? Provider { get; set; }
    
    /// <summary>
    /// Model to use (optional, will use provider default if not specified)
    /// </summary>
    public string? Model { get; set; }
    
    /// <summary>
    /// Number of completions to generate
    /// </summary>
    [Range(1, 10, ErrorMessage = "NumberOfCompletions must be between 1 and 10")]
    public int NumberOfCompletions { get; set; } = 1;
    
    /// <summary>
    /// Stop sequences to end generation
    /// </summary>
    public List<string>? StopSequences { get; set; }
    
    /// <summary>
    /// Whether to echo the prompt in the response
    /// </summary>
    public bool Echo { get; set; } = false;
}

/// <summary>
/// Text completion response model
/// </summary>
public class CompletionResponse
{
    /// <summary>
    /// The generated completions
    /// </summary>
    public List<CompletionChoice> Choices { get; set; } = new();
    
    /// <summary>
    /// The provider that generated the response
    /// </summary>
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// The model used for generation
    /// </summary>
    public string Model { get; set; } = string.Empty;
    
    /// <summary>
    /// Number of tokens in the prompt
    /// </summary>
    public int PromptTokens { get; set; }
    
    /// <summary>
    /// Number of tokens in the completion
    /// </summary>
    public int CompletionTokens { get; set; }
    
    /// <summary>
    /// Total number of tokens used
    /// </summary>
    public int TotalTokens { get; set; }
    
    /// <summary>
    /// Time taken to generate the response
    /// </summary>
    public TimeSpan Duration { get; set; }
    
    /// <summary>
    /// Timestamp when the response was generated
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Whether the response was successful
    /// </summary>
    public bool Success { get; set; } = true;
    
    /// <summary>
    /// Error message if the response was not successful
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Additional metadata from the provider
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Represents a single completion choice
/// </summary>
public class CompletionChoice
{
    /// <summary>
    /// The generated text
    /// </summary>
    public string Text { get; set; } = string.Empty;
    
    /// <summary>
    /// The index of this choice
    /// </summary>
    public int Index { get; set; }
    
    /// <summary>
    /// The reason the generation finished
    /// </summary>
    public string? FinishReason { get; set; }
    
    /// <summary>
    /// Log probabilities for the tokens (if requested)
    /// </summary>
    public Dictionary<string, double>? LogProbs { get; set; }
}
