// using Microsoft.AspNetCore.Http;
// using Microsoft.Extensions.Configuration;
// using Microsoft.Extensions.Logging;
//
// namespace Ai.Integrate.Shared.Middleware;
//
// /// <summary>
// /// Middleware for API key authentication
// /// </summary>
// public class ApiKeyAuthMiddleware(
//     RequestDelegate next,
//     IConfiguration configuration,
//     ILogger<ApiKeyAuthMiddleware> logger)
// {
//     private readonly RequestDelegate _next = next ?? throw new ArgumentNullException(nameof(next));
//     private readonly IConfiguration _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
//     private readonly ILogger<ApiKeyAuthMiddleware> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
//     private const string ApiKeyHeaderName = "X-API-Key";
//     private const string ApiKeyQueryParam = "apikey";
//
//     public async Task InvokeAsync(HttpContext context)
//     {
//         // Skip authentication for health checks and OpenAPI endpoints
//         if (ShouldSkipAuthentication(context.Request.Path))
//         {
//             await _next(context);
//             return;
//         }
//
//         var apiKey = ExtractApiKey(context.Request);
//         
//         if (string.IsNullOrEmpty(apiKey))
//         {
//             _logger.LogWarning("API key not provided for request to {Path}", context.Request.Path);
//             await WriteUnauthorizedResponse(context, "API key is required");
//             return;
//         }
//
//         if (!IsValidApiKey(apiKey))
//         {
//             _logger.LogWarning("Invalid API key provided for request to {Path}", context.Request.Path);
//             await WriteUnauthorizedResponse(context, "Invalid API key");
//             return;
//         }
//
//         _logger.LogDebug("API key authentication successful for request to {Path}", context.Request.Path);
//         await _next(context);
//     }
//
//     private static bool ShouldSkipAuthentication(PathString path)
//     {
//         var pathValue = path.Value?.ToLowerInvariant() ?? string.Empty;
//         
//         return pathValue.StartsWith("/health") ||
//                pathValue.StartsWith("/openapi") ||
//                pathValue.StartsWith("/scalar") ||
//                pathValue.StartsWith("/swagger") ||
//                pathValue == "/" ||
//                pathValue == "/favicon.ico";
//     }
//
//     private static string? ExtractApiKey(HttpRequest request)
//     {
//         // Try header first
//         if (request.Headers.TryGetValue(ApiKeyHeaderName, out var headerValue))
//         {
//             return headerValue.FirstOrDefault();
//         }
//
//         // Try query parameter
//         if (request.Query.TryGetValue(ApiKeyQueryParam, out var queryValue))
//         {
//             return queryValue.FirstOrDefault();
//         }
//
//         return null;
//     }
//
//     private bool IsValidApiKey(string apiKey)
//     {
//         var validApiKeys = _configuration.GetSection("Authentication:ApiKeys").Get<string[]>();
//         
//         if (validApiKeys == null || validApiKeys.Length == 0)
//         {
//             _logger.LogWarning("No API keys configured in appsettings");
//             return false;
//         }
//
//         return validApiKeys.Contains(apiKey);
//     }
//
//     private static async Task WriteUnauthorizedResponse(HttpContext context, string message)
//     {
//         context.Response.StatusCode = 401;
//         context.Response.ContentType = "application/json";
//         
//         var response = new
//         {
//             error = "Unauthorized",
//             message = message,
//             timestamp = DateTime.UtcNow
//         };
//
//         await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
//     }
// }
