namespace Ai.Integrate.Application.Dtos;

/// <summary>
/// Response model for available providers
/// </summary>
public class ProvidersResponse
{
    /// <summary>
    /// List of available providers
    /// </summary>
    public List<ProviderInfo> Providers { get; set; } = new();
    
    /// <summary>
    /// Default provider name
    /// </summary>
    public string DefaultProvider { get; set; } = string.Empty;
    
    /// <summary>
    /// Timestamp when the response was generated
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Whether the response was successful
    /// </summary>
    public bool Success { get; set; } = true;
    
    /// <summary>
    /// Error message if the response was not successful
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Information about an AI provider
/// </summary>
public class ProviderInfo
{
    /// <summary>
    /// Provider name
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Display name for the provider
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;
    
    /// <summary>
    /// Provider description
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether the provider is enabled
    /// </summary>
    public bool Enabled { get; set; }
    
    /// <summary>
    /// Whether the provider is currently available
    /// </summary>
    public bool Available { get; set; }
    
    /// <summary>
    /// Provider status message
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// List of supported capabilities
    /// </summary>
    public List<string> Capabilities { get; set; } = new();
    
    /// <summary>
    /// Default model for this provider
    /// </summary>
    public string DefaultModel { get; set; } = string.Empty;
    
    /// <summary>
    /// Number of available models
    /// </summary>
    public int ModelCount { get; set; }
    
    /// <summary>
    /// Provider configuration status
    /// </summary>
    public bool Configured { get; set; }
    
    /// <summary>
    /// Last availability check timestamp
    /// </summary>
    public DateTime LastChecked { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Response model for provider models
/// </summary>
public class ModelsResponse
{
    /// <summary>
    /// Provider name
    /// </summary>
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// List of available models
    /// </summary>
    public List<ModelInfo> Models { get; set; } = new();
    
    /// <summary>
    /// Default model for this provider
    /// </summary>
    public string DefaultModel { get; set; } = string.Empty;
    
    /// <summary>
    /// Timestamp when the response was generated
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Whether the response was successful
    /// </summary>
    public bool Success { get; set; } = true;
    
    /// <summary>
    /// Error message if the response was not successful
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Information about an AI model
/// </summary>
public class ModelInfo
{
    /// <summary>
    /// Model identifier
    /// </summary>
    public string Id { get; set; } = string.Empty;
    
    /// <summary>
    /// Display name for the model
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Model description
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether the model is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;
    
    /// <summary>
    /// Maximum context length in tokens
    /// </summary>
    public int MaxTokens { get; set; }
    
    /// <summary>
    /// Model capabilities
    /// </summary>
    public List<string> Capabilities { get; set; } = new();
    
    /// <summary>
    /// Model type (chat, completion, embedding, etc.)
    /// </summary>
    public string Type { get; set; } = string.Empty;
    
    /// <summary>
    /// Model version or release date
    /// </summary>
    public string Version { get; set; } = string.Empty;
    
    /// <summary>
    /// Cost per token (if available)
    /// </summary>
    public decimal? CostPerToken { get; set; }
    
    /// <summary>
    /// Whether this is the default model for the provider
    /// </summary>
    public bool IsDefault { get; set; }
}
