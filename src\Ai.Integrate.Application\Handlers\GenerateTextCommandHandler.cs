using Ai.Integrate.Application.Commands;
using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Ai.Integrate.Application.Handlers;

/// <summary>
/// Handler for GenerateTextCommand
/// </summary>
public class GenerateTextCommandHandler : IRequestHandler<GenerateTextCommand, GenerateResponse>
{
    private readonly IEnumerable<IAIService> _aiServices;
    private readonly ILogger<GenerateTextCommandHandler> _logger;

    public GenerateTextCommandHandler(
        IEnumerable<IAIService> aiServices,
        ILogger<GenerateTextCommandHandler> logger)
    {
        _aiServices = aiServices ?? throw new ArgumentNullException(nameof(aiServices));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<GenerateResponse> Handle(GenerateTextCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling GenerateTextCommand with provider: {Provider}", request.Provider ?? "default");

        try
        {
            var aiService = GetAIService(request.Provider);
            
            if (aiService == null)
            {
                _logger.LogError("No AI service found for provider: {Provider}", request.Provider ?? "default");
                return new GenerateResponse
                {
                    Success = false,
                    ErrorMessage = $"No AI service found for provider: {request.Provider ?? "default"}"
                };
            }

            // Check if the service is available
            var isAvailable = await aiService.IsAvailableAsync(cancellationToken);
            if (!isAvailable)
            {
                _logger.LogWarning("AI service {Provider} is not available", aiService.ProviderName);
                return new GenerateResponse
                {
                    Success = false,
                    ErrorMessage = $"AI service {aiService.ProviderName} is not available"
                };
            }

            // Convert command to request
            var generateRequest = new GenerateRequest
            {
                Prompt = request.Prompt,
                MaxTokens = request.MaxTokens,
                Temperature = request.Temperature,
                TopP = request.TopP,
                Provider = request.Provider,
                Model = request.Model,
                SystemMessage = request.SystemMessage,
                Stream = request.Stream
            };

            var startTime = DateTime.UtcNow;
            var response = await aiService.GenerateTextAsync(generateRequest, cancellationToken);
            var endTime = DateTime.UtcNow;
            
            response.Duration = endTime - startTime;
            response.Timestamp = endTime;
            
            _logger.LogInformation("Text generation completed successfully in {Duration}ms", 
                response.Duration.TotalMilliseconds);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during text generation");
            return new GenerateResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred during text generation: {ex.Message}"
            };
        }
    }

    private IAIService? GetAIService(string? providerName)
    {
        if (string.IsNullOrEmpty(providerName))
        {
            // Return the first available service as default
            return _aiServices.FirstOrDefault();
        }

        return _aiServices.FirstOrDefault(s => 
            string.Equals(s.ProviderName, providerName, StringComparison.OrdinalIgnoreCase));
    }
}
