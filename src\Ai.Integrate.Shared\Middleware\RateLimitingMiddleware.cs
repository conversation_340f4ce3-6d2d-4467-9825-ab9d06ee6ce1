using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Net;

namespace Ai.Integrate.Shared.Middleware;

/// <summary>
/// Rate limiting middleware
/// </summary>
public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IConfiguration _configuration;
    private readonly ILogger<RateLimitingMiddleware> _logger;
    private readonly ConcurrentDictionary<string, ClientRateLimit> _clients = new();

    public RateLimitingMiddleware(
        RequestDelegate next,
        IConfiguration configuration,
        ILogger<RateLimitingMiddleware> logger)
    {
        _next = next ?? throw new ArgumentNullException(nameof(next));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Skip rate limiting for health checks and OpenAPI endpoints
        if (ShouldSkipRateLimit(context.Request.Path))
        {
            await _next(context);
            return;
        }

        var clientId = GetClientIdentifier(context);
        var rateLimitConfig = GetRateLimitConfig(context.Request.Path);

        if (rateLimitConfig == null)
        {
            await _next(context);
            return;
        }

        var clientRateLimit = _clients.GetOrAdd(clientId, _ => new ClientRateLimit());
        
        if (!clientRateLimit.IsAllowed(rateLimitConfig))
        {
            _logger.LogWarning("Rate limit exceeded for client {ClientId} on path {Path}", 
                clientId, context.Request.Path);
            
            await WriteRateLimitResponse(context, clientRateLimit, rateLimitConfig);
            return;
        }

        clientRateLimit.RecordRequest();
        
        // Add rate limit headers
        AddRateLimitHeaders(context, clientRateLimit, rateLimitConfig);
        
        await _next(context);
    }

    private static bool ShouldSkipRateLimit(PathString path)
    {
        var pathValue = path.Value?.ToLowerInvariant() ?? string.Empty;
        
        return pathValue.StartsWith("/health") ||
               pathValue.StartsWith("/openapi") ||
               pathValue.StartsWith("/scalar") ||
               pathValue.StartsWith("/swagger") ||
               pathValue == "/" ||
               pathValue == "/favicon.ico";
    }

    private static string GetClientIdentifier(HttpContext context)
    {
        // Try to get API key first
        if (context.Request.Headers.TryGetValue("X-API-Key", out var apiKey))
        {
            return $"apikey:{apiKey.FirstOrDefault()}";
        }

        // Fall back to IP address
        var ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        return $"ip:{ipAddress}";
    }

    private RateLimitConfig? GetRateLimitConfig(PathString path)
    {
        var pathValue = path.Value?.ToLowerInvariant() ?? string.Empty;

        // Different rate limits for different endpoints
        if (pathValue.StartsWith("/api/chat") || pathValue.StartsWith("/api/generate") || pathValue.StartsWith("/api/completion"))
        {
            return new RateLimitConfig
            {
                RequestsPerMinute = _configuration.GetValue<int>("RateLimit:AI:RequestsPerMinute", 60),
                RequestsPerHour = _configuration.GetValue<int>("RateLimit:AI:RequestsPerHour", 1000),
                BurstLimit = _configuration.GetValue<int>("RateLimit:AI:BurstLimit", 10)
            };
        }

        if (pathValue.StartsWith("/api/"))
        {
            return new RateLimitConfig
            {
                RequestsPerMinute = _configuration.GetValue<int>("RateLimit:API:RequestsPerMinute", 120),
                RequestsPerHour = _configuration.GetValue<int>("RateLimit:API:RequestsPerHour", 2000),
                BurstLimit = _configuration.GetValue<int>("RateLimit:API:BurstLimit", 20)
            };
        }

        return null;
    }

    private static void AddRateLimitHeaders(HttpContext context, ClientRateLimit clientRateLimit, RateLimitConfig config)
    {
        var now = DateTime.UtcNow;
        var minuteWindow = now.AddMinutes(-1);
        var hourWindow = now.AddHours(-1);

        var requestsInLastMinute = clientRateLimit.GetRequestCount(minuteWindow);
        var requestsInLastHour = clientRateLimit.GetRequestCount(hourWindow);

        context.Response.Headers.Add("X-RateLimit-Limit-Minute", config.RequestsPerMinute.ToString());
        context.Response.Headers.Add("X-RateLimit-Remaining-Minute", Math.Max(0, config.RequestsPerMinute - requestsInLastMinute).ToString());
        context.Response.Headers.Add("X-RateLimit-Limit-Hour", config.RequestsPerHour.ToString());
        context.Response.Headers.Add("X-RateLimit-Remaining-Hour", Math.Max(0, config.RequestsPerHour - requestsInLastHour).ToString());
        context.Response.Headers.Add("X-RateLimit-Reset", ((DateTimeOffset)now.AddMinutes(1)).ToUnixTimeSeconds().ToString());
    }

    private static async Task WriteRateLimitResponse(HttpContext context, ClientRateLimit clientRateLimit, RateLimitConfig config)
    {
        context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
        context.Response.ContentType = "application/json";

        var now = DateTime.UtcNow;
        var minuteWindow = now.AddMinutes(-1);
        var requestsInLastMinute = clientRateLimit.GetRequestCount(minuteWindow);
        var retryAfter = 60 - (int)(now - minuteWindow).TotalSeconds;

        context.Response.Headers.Add("Retry-After", retryAfter.ToString());
        AddRateLimitHeaders(context, clientRateLimit, config);

        var response = new
        {
            error = "Rate limit exceeded",
            message = $"Too many requests. Limit: {config.RequestsPerMinute} requests per minute",
            retryAfter = retryAfter,
            timestamp = DateTime.UtcNow
        };

        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }
}

/// <summary>
/// Rate limit configuration
/// </summary>
public class RateLimitConfig
{
    public int RequestsPerMinute { get; set; }
    public int RequestsPerHour { get; set; }
    public int BurstLimit { get; set; }
}

/// <summary>
/// Client rate limit tracker
/// </summary>
public class ClientRateLimit
{
    private readonly List<DateTime> _requests = new();
    private readonly object _lock = new();

    public bool IsAllowed(RateLimitConfig config)
    {
        lock (_lock)
        {
            var now = DateTime.UtcNow;
            CleanupOldRequests(now);

            var minuteWindow = now.AddMinutes(-1);
            var hourWindow = now.AddHours(-1);

            var requestsInLastMinute = _requests.Count(r => r >= minuteWindow);
            var requestsInLastHour = _requests.Count(r => r >= hourWindow);

            return requestsInLastMinute < config.RequestsPerMinute && 
                   requestsInLastHour < config.RequestsPerHour;
        }
    }

    public void RecordRequest()
    {
        lock (_lock)
        {
            _requests.Add(DateTime.UtcNow);
            CleanupOldRequests(DateTime.UtcNow);
        }
    }

    public int GetRequestCount(DateTime since)
    {
        lock (_lock)
        {
            return _requests.Count(r => r >= since);
        }
    }

    private void CleanupOldRequests(DateTime now)
    {
        var cutoff = now.AddHours(-1);
        _requests.RemoveAll(r => r < cutoff);
    }
}
