﻿namespace Ai.Integrate.Infrastructure.Config;

/// <summary>
/// Configuration settings for AI providers
/// </summary>
public class AISettings
{
    public const string SectionName = "AISettings";

    /// <summary>
    /// Default provider to use when none is specified
    /// </summary>
    public string DefaultProvider { get; set; } = "OpenAI";

    /// <summary>
    /// OpenAI configuration
    /// </summary>
    public OpenAISettings OpenAI { get; set; } = new();

    /// <summary>
    /// Claude configuration
    /// </summary>
    public ClaudeSettings Claude { get; set; } = new();

    /// <summary>
    /// Google configuration
    /// </summary>
    public GoogleSettings Google { get; set; } = new();

    /// <summary>
    /// OpenRouter configuration
    /// </summary>
    public OpenRouterSettings OpenRouter { get; set; } = new();

    /// <summary>
    /// Llama configuration
    /// </summary>
    public LlamaSettings Llama { get; set; } = new();

    /// <summary>
    /// <PERSON><PERSON>hain configuration
    /// </summary>
    public LangChainSettings LangChain { get; set; } = new();

    /// <summary>
    /// HTTP client configuration
    /// </summary>
    public HttpClientSettings HttpClient { get; set; } = new();
}

public class OpenAISettings
{
    public string ApiKey { get; set; } = string.Empty;
    public string BaseUrl { get; set; } = "https://api.openai.com/v1";
    public string DefaultModel { get; set; } = "gpt-3.5-turbo";
    public bool Enabled { get; set; } = true;
}

public class ClaudeSettings
{
    public string ApiKey { get; set; } = string.Empty;
    public string BaseUrl { get; set; } = "https://api.anthropic.com";
    public string DefaultModel { get; set; } = "claude-3-sonnet-20240229";
    public bool Enabled { get; set; } = true;
}

public class GoogleSettings
{
    public string ApiKey { get; set; } = string.Empty;
    public string BaseUrl { get; set; } = "https://generativelanguage.googleapis.com";
    public string DefaultModel { get; set; } = "gemini-pro";
    public bool Enabled { get; set; } = true;
}

public class OpenRouterSettings
{
    public string ApiKey { get; set; } = string.Empty;
    public string BaseUrl { get; set; } = "https://openrouter.ai/api/v1";
    public string DefaultModel { get; set; } = "openai/gpt-3.5-turbo";
    public bool Enabled { get; set; } = true;
}

public class LlamaSettings
{
    public string BaseUrl { get; set; } = "http://localhost:11434";
    public string DefaultModel { get; set; } = "llama2";
    public bool Enabled { get; set; } = false;
}

public class LangChainSettings
{
    public string BaseUrl { get; set; } = "http://localhost:8000";
    public string ApiKey { get; set; } = string.Empty;
    public bool Enabled { get; set; } = false;
}

public class HttpClientSettings
{
    public int TimeoutSeconds { get; set; } = 30;
    public int RetryCount { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 2;
    public bool EnableCircuitBreaker { get; set; } = true;
    public int CircuitBreakerFailureThreshold { get; set; } = 5;
    public int CircuitBreakerTimeoutSeconds { get; set; } = 60;
}