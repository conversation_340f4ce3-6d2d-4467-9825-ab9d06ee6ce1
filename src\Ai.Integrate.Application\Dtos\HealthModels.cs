namespace Ai.Integrate.Application.Dtos;

/// <summary>
/// Enhanced health status response
/// </summary>
public class HealthStatusResponse
{
    /// <summary>
    /// Overall service status
    /// </summary>
    public string Status { get; set; } = "Healthy";
    
    /// <summary>
    /// Service version
    /// </summary>
    public string Version { get; set; } = string.Empty;
    
    /// <summary>
    /// Service uptime
    /// </summary>
    public TimeSpan Uptime { get; set; }
    
    /// <summary>
    /// Timestamp when the health check was performed
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Provider availability status
    /// </summary>
    public List<ProviderHealthStatus> Providers { get; set; } = new();
    
    /// <summary>
    /// System information (if requested)
    /// </summary>
    public SystemInfo? SystemInfo { get; set; }
    
    /// <summary>
    /// Additional health check details
    /// </summary>
    public Dictionary<string, object>? Details { get; set; }
    
    /// <summary>
    /// Whether all critical services are healthy
    /// </summary>
    public bool IsHealthy { get; set; } = true;
    
    /// <summary>
    /// List of any warnings or issues
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Health status for a specific provider
/// </summary>
public class ProviderHealthStatus
{
    /// <summary>
    /// Provider name
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Provider status (Healthy, Degraded, Unhealthy)
    /// </summary>
    public string Status { get; set; } = "Unknown";
    
    /// <summary>
    /// Whether the provider is available
    /// </summary>
    public bool Available { get; set; }
    
    /// <summary>
    /// Response time for the last health check
    /// </summary>
    public TimeSpan ResponseTime { get; set; }
    
    /// <summary>
    /// Last successful check timestamp
    /// </summary>
    public DateTime LastSuccessfulCheck { get; set; }
    
    /// <summary>
    /// Error message if the provider is unhealthy
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Additional provider-specific details
    /// </summary>
    public Dictionary<string, object>? Details { get; set; }
}

/// <summary>
/// System information
/// </summary>
public class SystemInfo
{
    /// <summary>
    /// Operating system information
    /// </summary>
    public string OperatingSystem { get; set; } = string.Empty;
    
    /// <summary>
    /// .NET runtime version
    /// </summary>
    public string RuntimeVersion { get; set; } = string.Empty;
    
    /// <summary>
    /// Available memory in MB
    /// </summary>
    public long AvailableMemoryMB { get; set; }
    
    /// <summary>
    /// Used memory in MB
    /// </summary>
    public long UsedMemoryMB { get; set; }
    
    /// <summary>
    /// CPU usage percentage
    /// </summary>
    public double CpuUsagePercent { get; set; }
    
    /// <summary>
    /// Number of active threads
    /// </summary>
    public int ThreadCount { get; set; }
    
    /// <summary>
    /// Application start time
    /// </summary>
    public DateTime StartTime { get; set; }
}
