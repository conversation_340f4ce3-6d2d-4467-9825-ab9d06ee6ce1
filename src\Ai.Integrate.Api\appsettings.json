{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Authentication": {"ApiKeys": ["your-api-key-here", "another-api-key"]}, "RateLimit": {"AI": {"RequestsPerMinute": 60, "RequestsPerHour": 1000, "BurstLimit": 10}, "API": {"RequestsPerMinute": 120, "RequestsPerHour": 2000, "BurstLimit": 20}}, "Cache": {"DefaultExpirationMinutes": 60, "ProviderAvailabilityMinutes": 5, "ModelInfoMinutes": 1440, "ConversationMinutes": 30}, "AISettings": {"DefaultProvider": "OpenAI", "OpenAI": {"ApiKey": "", "BaseUrl": "https://api.openai.com/v1", "DefaultModel": "gpt-3.5-turbo", "Enabled": true, "MaxTokensLimit": 4096, "SupportedModels": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"]}, "Claude": {"ApiKey": "", "BaseUrl": "https://api.anthropic.com", "DefaultModel": "claude-3-sonnet-20240229", "Enabled": false, "MaxTokensLimit": 200000, "SupportedModels": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]}, "Google": {"ApiKey": "", "BaseUrl": "https://generativelanguage.googleapis.com", "DefaultModel": "gemini-pro", "Enabled": false, "MaxTokensLimit": 32768, "SupportedModels": ["gemini-pro", "gemini-pro-vision", "gemini-ultra"]}, "OpenRouter": {"ApiKey": "", "BaseUrl": "https://openrouter.ai/api/v1", "DefaultModel": "openai/gpt-3.5-turbo", "Enabled": false, "MaxTokensLimit": 8192, "SupportedModels": ["openai/gpt-4", "openai/gpt-3.5-turbo", "anthropic/claude-2"]}, "Llama": {"BaseUrl": "http://localhost:11434", "DefaultModel": "llama2", "Enabled": false, "MaxTokensLimit": 4096, "SupportedModels": ["llama2", "llama2:13b", "codellama"]}, "LangChain": {"BaseUrl": "http://localhost:8000", "ApiKey": "", "Enabled": false, "MaxTokensLimit": 4096, "SupportedModels": ["langchain-default"]}, "HttpClient": {"TimeoutSeconds": 30, "RetryCount": 3, "RetryDelaySeconds": 2, "EnableCircuitBreaker": true, "CircuitBreakerFailureThreshold": 5, "CircuitBreakerTimeoutSeconds": 60}}}