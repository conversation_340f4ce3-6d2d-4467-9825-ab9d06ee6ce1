{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Authentication": {"ApiKeys": ["your-api-key-here", "another-api-key"]}, "AISettings": {"DefaultProvider": "OpenAI", "OpenAI": {"ApiKey": "", "BaseUrl": "https://api.openai.com/v1", "DefaultModel": "gpt-3.5-turbo", "Enabled": true}, "Claude": {"ApiKey": "", "BaseUrl": "https://api.anthropic.com", "DefaultModel": "claude-3-sonnet-20240229", "Enabled": false}, "Google": {"ApiKey": "", "BaseUrl": "https://generativelanguage.googleapis.com", "DefaultModel": "gemini-pro", "Enabled": false}, "OpenRouter": {"ApiKey": "", "BaseUrl": "https://openrouter.ai/api/v1", "DefaultModel": "openai/gpt-3.5-turbo", "Enabled": false}, "Llama": {"BaseUrl": "http://localhost:11434", "DefaultModel": "llama2", "Enabled": false}, "LangChain": {"BaseUrl": "http://localhost:8000", "ApiKey": "", "Enabled": false}, "HttpClient": {"TimeoutSeconds": 30, "RetryCount": 3, "RetryDelaySeconds": 2, "EnableCircuitBreaker": true, "CircuitBreakerFailureThreshold": 5, "CircuitBreakerTimeoutSeconds": 60}}}