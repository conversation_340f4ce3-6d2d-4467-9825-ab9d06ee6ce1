{"format": 1, "restore": {"D:\\Code\\Ai.Integration.Service\\Ai.Integrate.Modes\\Ai.Integrate.Modes.csproj": {}}, "projects": {"D:\\Code\\Ai.Integration.Service\\Ai.Integrate.Modes\\Ai.Integrate.Modes.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Code\\Ai.Integration.Service\\Ai.Integrate.Modes\\Ai.Integrate.Modes.csproj", "projectName": "Ai.Integrate.Modes", "projectPath": "D:\\Code\\Ai.Integration.Service\\Ai.Integrate.Modes\\Ai.Integrate.Modes.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Code\\Ai.Integration.Service\\Ai.Integrate.Modes\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Net.Sdk.Compilers.Toolset", "version": "[9.0.203, 9.0.203]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}