# AI Integration Service

## Overview

This is a production-ready ASP.NET Core Minimal API service that provides a unified interface for multiple AI providers including OpenAI, Claude, Google, OpenRouter, Llama, and LangChain.

## Features

### 1. **Clean Architecture Implementation**

- **API Layer**: Minimal API endpoints with proper routing and validation
- **Application Layer**: Use cases and business logic
- **Infrastructure Layer**: AI provider implementations and external service integrations
- **Domain Layer**: Core entities and value objects
- **Shared Layer**: Common utilities, middleware, and extensions

### 2. **Polly Integration for Resilience**

- **Retry Policies**: Exponential backoff for transient failures
- **Circuit Breaker**: Prevents cascading failures
- **Timeout Handling**: Configurable request timeouts
- **HTTP Client Factory**: Proper HttpClient lifecycle management

### 3. **Comprehensive Middleware Pipeline**

- **Global Exception Handling**: Consistent error responses
- **API Key Authentication**: Secure endpoint access
- **CORS Support**: Cross-origin request handling
- **Request Logging**: Structured logging with Serilog

### 4. **OpenAPI/Scalar Documentation**

- **Interactive API Documentation**: Scalar UI with BluePlanet theme
- **Security Definitions**: API key authentication support
- **Comprehensive Schemas**: Request/response models with validation
- **Environment-Aware**: Development vs Production configurations

### 5. **Dependency Injection & Configuration**

- **Scoped Services**: Proper service lifetime management
- **Configuration Binding**: Type-safe settings from appsettings.json
- **FluentValidation**: Comprehensive request validation
- **Health Checks**: Service availability monitoring

## Configuration

### appsettings.json Structure

```json
{
  "Authentication": {
    "ApiKeys": ["your-api-key-here"]
  },
  "AISettings": {
    "DefaultProvider": "OpenAI",
    "OpenAI": {
      "ApiKey": "your-openai-key",
      "BaseUrl": "https://api.openai.com/v1",
      "DefaultModel": "gpt-3.5-turbo",
      "Enabled": true
    },
    "HttpClient": {
      "TimeoutSeconds": 30,
      "RetryCount": 3,
      "RetryDelaySeconds": 2,
      "EnableCircuitBreaker": true,
      "CircuitBreakerFailureThreshold": 5,
      "CircuitBreakerTimeoutSeconds": 60
    }
  }
}
```

## API Endpoints

### GET /api/providers

Get list of available AI providers with their status and capabilities.

**Query Parameters:**

- `enabledOnly` (bool): Return only enabled providers (default: false)
- `checkAvailability` (bool): Check provider availability status (default: true)

**Response:**

```json
{
  "providers": [
    {
      "name": "OpenAI",
      "displayName": "OpenAI",
      "description": "OpenAI GPT models",
      "enabled": true,
      "available": true,
      "status": "Available",
      "capabilities": ["text-generation", "chat", "completion", "streaming"],
      "defaultModel": "gpt-3.5-turbo",
      "modelCount": 15,
      "configured": true,
      "lastChecked": "2024-01-01T12:00:00Z"
    }
  ],
  "defaultProvider": "OpenAI",
  "success": true
}
```

### GET /api/providers/{provider}/models

Get available models for a specific provider.

**Path Parameters:**

- `provider` (string): Provider name (OpenAI, Claude, Google, etc.)

**Query Parameters:**

- `enabledOnly` (bool): Return only enabled models (default: true)
- `includeCapabilities` (bool): Include model capabilities (default: false)

**Response:**

```json
{
  "provider": "OpenAI",
  "models": [
    {
      "id": "gpt-3.5-turbo",
      "name": "GPT-3.5 Turbo",
      "description": "Fast and efficient model",
      "enabled": true,
      "maxTokens": 4096,
      "capabilities": ["chat", "completion", "streaming"],
      "type": "chat",
      "version": "2023-06-13",
      "isDefault": true
    }
  ],
  "defaultModel": "gpt-3.5-turbo",
  "success": true
}
```

### POST /api/chat

Chat completion with conversation history support.

**Request Body:**

```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant"
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "maxTokens": 1000,
  "temperature": 0.7,
  "topP": 1.0,
  "provider": "OpenAI",
  "model": "gpt-3.5-turbo",
  "stream": false,
  "conversationId": "conv-123"
}
```

**Response:**

```json
{
  "message": {
    "role": "assistant",
    "content": "Hello! I'm doing well, thank you for asking...",
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "provider": "OpenAI",
  "model": "gpt-3.5-turbo",
  "promptTokens": 25,
  "completionTokens": 50,
  "totalTokens": 75,
  "duration": "00:00:01.5000000",
  "conversationId": "conv-123",
  "success": true
}
```

### POST /api/completion

Text completion endpoint.

**Request Body:**

```json
{
  "prompt": "The future of artificial intelligence is",
  "maxTokens": 500,
  "temperature": 0.8,
  "topP": 1.0,
  "provider": "OpenAI",
  "model": "gpt-3.5-turbo",
  "numberOfCompletions": 2,
  "stopSequences": ["\n\n"],
  "echo": false
}
```

**Response:**

```json
{
  "choices": [
    {
      "text": "bright and full of possibilities...",
      "index": 0,
      "finishReason": "stop"
    },
    {
      "text": "evolving rapidly with new breakthroughs...",
      "index": 1,
      "finishReason": "stop"
    }
  ],
  "provider": "OpenAI",
  "model": "gpt-3.5-turbo",
  "promptTokens": 10,
  "completionTokens": 100,
  "totalTokens": 110,
  "success": true
}
```

### POST /api/generate

Generate text using AI providers (backward compatibility).

**Request Body:**

```json
{
  "prompt": "Write a short story about AI",
  "maxTokens": 1000,
  "temperature": 0.7,
  "topP": 1.0,
  "provider": "OpenAI",
  "model": "gpt-3.5-turbo",
  "systemMessage": "You are a creative writer",
  "stream": false
}
```

**Response:**

```json
{
  "text": "Generated text content...",
  "provider": "OpenAI",
  "model": "gpt-3.5-turbo",
  "promptTokens": 15,
  "completionTokens": 200,
  "totalTokens": 215,
  "duration": "00:00:02.1234567",
  "timestamp": "2024-01-01T12:00:00Z",
  "success": true
}
```

### GET /api/health

Enhanced health check with provider availability status.

**Query Parameters:**

- `includeProviderStatus` (bool): Include provider status (default: true)
- `includeSystemInfo` (bool): Include system information (default: false)

**Response:**

```json
{
  "status": "Healthy",
  "version": "1.0.0",
  "uptime": "01:30:45",
  "providers": [
    {
      "name": "OpenAI",
      "status": "Healthy",
      "available": true,
      "responseTime": "00:00:00.250",
      "lastSuccessfulCheck": "2024-01-01T12:00:00Z"
    }
  ],
  "isHealthy": true,
  "warnings": []
}
```

### GET /health

Basic health check endpoint for monitoring service availability.

## Authentication

The API uses API key authentication. Include your API key in requests using:

- Header: `X-API-Key: your-api-key-here`
- Query parameter: `?apikey=your-api-key-here`

## Development

### Prerequisites

- .NET 9.0 SDK
- Visual Studio 2022 or VS Code
- AI provider API keys (OpenAI, Claude, etc.)

### Running the Service

1. Configure your API keys in `appsettings.Development.json`
2. Run: `dotnet run --project src/Ai.Integrate.Api`
3. Navigate to: `https://localhost:7289/scalar/v1` for API documentation

### Testing

Use the Scalar UI or tools like Postman to test the endpoints. Make sure to include your API key in requests.

## Production Deployment

### Environment Variables

Set the following environment variables in production:

- `AISettings__OpenAI__ApiKey`
- `AISettings__Claude__ApiKey`
- `Authentication__ApiKeys__0`

### Logging

Logs are written to:

- Console (structured JSON in production)
- File: `logs/ai-integration-{date}.txt`

### Health Monitoring

Monitor the `/health` endpoint for service availability.

## Architecture Benefits

1. **Maintainability**: Clean separation of concerns
2. **Testability**: Dependency injection enables easy unit testing
3. **Scalability**: Stateless design with proper resource management
4. **Reliability**: Polly policies handle transient failures
5. **Security**: API key authentication and input validation
6. **Observability**: Comprehensive logging and health checks
