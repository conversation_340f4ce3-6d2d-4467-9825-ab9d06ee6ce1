using Ai.Integrate.Application.Dtos;
using MediatR;

namespace Ai.Integrate.Application.Commands;

/// <summary>
/// Command for text completion
/// </summary>
public class TextCompletionCommand : IRequest<CompletionResponse>
{
    /// <summary>
    /// The text prompt to complete
    /// </summary>
    public string Prompt { get; set; } = string.Empty;
    
    /// <summary>
    /// Maximum number of tokens to generate
    /// </summary>
    public int MaxTokens { get; set; } = 1000;
    
    /// <summary>
    /// Temperature for randomness (0.0 to 2.0)
    /// </summary>
    public double Temperature { get; set; } = 0.7;
    
    /// <summary>
    /// Top-p sampling parameter
    /// </summary>
    public double TopP { get; set; } = 1.0;
    
    /// <summary>
    /// AI provider to use (optional, will use default if not specified)
    /// </summary>
    public string? Provider { get; set; }
    
    /// <summary>
    /// Model to use (optional, will use provider default if not specified)
    /// </summary>
    public string? Model { get; set; }
    
    /// <summary>
    /// Number of completions to generate
    /// </summary>
    public int NumberOfCompletions { get; set; } = 1;
    
    /// <summary>
    /// Stop sequences to end generation
    /// </summary>
    public List<string>? StopSequences { get; set; }
    
    /// <summary>
    /// Whether to echo the prompt in the response
    /// </summary>
    public bool Echo { get; set; } = false;
}
