using Ai.Integrate.Application.Dtos;

namespace Ai.Integrate.Application.Interfaces;

/// <summary>
/// Interface for chat-specific AI services
/// </summary>
public interface IChatService : IAIService
{
    /// <summary>
    /// Generate chat response with conversation history
    /// </summary>
    /// <param name="messages">List of conversation messages</param>
    /// <param name="options">Chat options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chat response</returns>
    Task<ChatResponse> ChatCompletionAsync(
        List<ChatMessage> messages, 
        ChatOptions options, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Stream chat response with conversation history
    /// </summary>
    /// <param name="messages">List of conversation messages</param>
    /// <param name="options">Chat options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Async enumerable of chat response chunks</returns>
    IAsyncEnumerable<ChatResponse> ChatCompletionStreamAsync(
        List<ChatMessage> messages, 
        ChatOptions options, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Chat options for AI providers
/// </summary>
public class ChatOptions
{
    /// <summary>
    /// Maximum number of tokens to generate
    /// </summary>
    public int MaxTokens { get; set; } = 1000;
    
    /// <summary>
    /// Temperature for randomness (0.0 to 2.0)
    /// </summary>
    public double Temperature { get; set; } = 0.7;
    
    /// <summary>
    /// Top-p sampling parameter
    /// </summary>
    public double TopP { get; set; } = 1.0;
    
    /// <summary>
    /// Model to use (optional, will use provider default if not specified)
    /// </summary>
    public string? Model { get; set; }
    
    /// <summary>
    /// Whether to stream the response
    /// </summary>
    public bool Stream { get; set; } = false;
    
    /// <summary>
    /// Conversation ID for tracking (optional)
    /// </summary>
    public string? ConversationId { get; set; }
    
    /// <summary>
    /// Stop sequences to end generation
    /// </summary>
    public List<string>? StopSequences { get; set; }
    
    /// <summary>
    /// Frequency penalty (-2.0 to 2.0)
    /// </summary>
    public double FrequencyPenalty { get; set; } = 0.0;
    
    /// <summary>
    /// Presence penalty (-2.0 to 2.0)
    /// </summary>
    public double PresencePenalty { get; set; } = 0.0;
}
