﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\AI.Integrate.Domain\AI.Integrate.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="FluentValidation" Version="12.0.0" />
      <PackageReference Include="MediatR" Version="12.5.0" />
      <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="OpenAI" Version="2.1.0" />
      <PackageReference Include="Polly" Version="8.5.2" />
      <PackageReference Include="RabbitMQ.Client" Version="7.1.2" />
      <PackageReference Include="RestSharp" Version="112.1.0" />
      <PackageReference Include="Scalar.AspNetCore" Version="2.4.3" />
      <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    </ItemGroup>

</Project>
