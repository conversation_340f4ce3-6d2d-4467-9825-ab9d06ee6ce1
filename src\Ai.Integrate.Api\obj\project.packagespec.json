﻿"restore":{"projectUniqueName":"D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Api\\Ai.Integrate.Api.csproj","projectName":"Ai.Integrate.Api","projectPath":"D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Api\\Ai.Integrate.Api.csproj","outputPath":"D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Api\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages","C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"],"originalTargetFrameworks":["net9.0"],"sources":{"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Application\\Ai.Integrate.Application.csproj":{"projectPath":"D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Application\\Ai.Integrate.Application.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"all"},"SdkAnalysisLevel":"9.0.200"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.AspNetCore.OpenApi":{"target":"Package","version":"[9.0.4, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.Net.Sdk.Compilers.Toolset","version":"[9.0.203, 9.0.203]"}],"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}