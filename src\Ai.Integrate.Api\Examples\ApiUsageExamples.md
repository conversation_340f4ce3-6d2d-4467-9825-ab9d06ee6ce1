# AI Integration Service - API Usage Examples

This document provides comprehensive examples of how to use all the endpoints in the AI Integration Service.

## Prerequisites

1. Ensure the service is running: `dotnet run --project src/Ai.Integrate.Api`
2. Configure your API keys in `appsettings.Development.json`
3. Use your API key in all requests

## Example Requests

### 1. Get Available Providers

```bash
curl -X GET "https://localhost:7289/api/providers?enabledOnly=false&checkAvailability=true" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

### 2. Get Models for OpenAI Provider

```bash
curl -X GET "https://localhost:7289/api/providers/OpenAI/models?enabledOnly=true&includeCapabilities=true" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

### 3. Chat Completion

```bash
curl -X POST "https://localhost:7289/api/chat" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful AI assistant specialized in explaining complex topics simply."
      },
      {
        "role": "user",
        "content": "Explain quantum computing in simple terms"
      }
    ],
    "maxTokens": 500,
    "temperature": 0.7,
    "provider": "OpenAI",
    "model": "gpt-3.5-turbo",
    "conversationId": "quantum-explanation-001"
  }'
```

### 4. Text Completion

```bash
curl -X POST "https://localhost:7289/api/completion" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "The benefits of renewable energy include",
    "maxTokens": 300,
    "temperature": 0.8,
    "numberOfCompletions": 3,
    "stopSequences": ["\n\n", "However"],
    "echo": false,
    "provider": "OpenAI"
  }'
```

### 5. Generate Text (Legacy Endpoint)

```bash
curl -X POST "https://localhost:7289/api/generate" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a creative story about a robot learning to paint",
    "maxTokens": 800,
    "temperature": 0.9,
    "systemMessage": "You are a creative storyteller",
    "provider": "OpenAI",
    "model": "gpt-3.5-turbo"
  }'
```

### 6. Enhanced Health Check

```bash
curl -X GET "https://localhost:7289/api/health?includeProviderStatus=true&includeSystemInfo=true" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

## PowerShell Examples

### Chat Completion with PowerShell

```powershell
$headers = @{
    "X-API-Key" = "your-api-key-here"
    "Content-Type" = "application/json"
}

$body = @{
    messages = @(
        @{
            role = "system"
            content = "You are a helpful assistant"
        },
        @{
            role = "user"
            content = "What are the latest trends in AI?"
        }
    )
    maxTokens = 500
    temperature = 0.7
    provider = "OpenAI"
} | ConvertTo-Json -Depth 3

Invoke-RestMethod -Uri "https://localhost:7289/api/chat" -Method POST -Headers $headers -Body $body
```

### Get Providers with PowerShell

```powershell
$headers = @{
    "X-API-Key" = "your-api-key-here"
}

Invoke-RestMethod -Uri "https://localhost:7289/api/providers" -Method GET -Headers $headers
```

## JavaScript/Node.js Examples

### Using Fetch API

```javascript
const apiKey = 'your-api-key-here';
const baseUrl = 'https://localhost:7289';

// Chat completion
async function chatCompletion() {
    const response = await fetch(`${baseUrl}/api/chat`, {
        method: 'POST',
        headers: {
            'X-API-Key': apiKey,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            messages: [
                {
                    role: 'system',
                    content: 'You are a helpful assistant'
                },
                {
                    role: 'user',
                    content: 'Explain machine learning in simple terms'
                }
            ],
            maxTokens: 400,
            temperature: 0.7,
            provider: 'OpenAI'
        })
    });
    
    const data = await response.json();
    console.log(data);
}

// Get providers
async function getProviders() {
    const response = await fetch(`${baseUrl}/api/providers`, {
        headers: {
            'X-API-Key': apiKey
        }
    });
    
    const data = await response.json();
    console.log(data);
}
```

## Python Examples

### Using requests library

```python
import requests
import json

API_KEY = 'your-api-key-here'
BASE_URL = 'https://localhost:7289'

headers = {
    'X-API-Key': API_KEY,
    'Content-Type': 'application/json'
}

# Chat completion
def chat_completion():
    data = {
        'messages': [
            {
                'role': 'system',
                'content': 'You are a helpful assistant'
            },
            {
                'role': 'user',
                'content': 'What is the future of artificial intelligence?'
            }
        ],
        'maxTokens': 500,
        'temperature': 0.7,
        'provider': 'OpenAI'
    }
    
    response = requests.post(f'{BASE_URL}/api/chat', headers=headers, json=data)
    return response.json()

# Get provider models
def get_provider_models(provider):
    response = requests.get(f'{BASE_URL}/api/providers/{provider}/models', headers=headers)
    return response.json()

# Text completion
def text_completion():
    data = {
        'prompt': 'The future of space exploration includes',
        'maxTokens': 300,
        'temperature': 0.8,
        'numberOfCompletions': 2,
        'provider': 'OpenAI'
    }
    
    response = requests.post(f'{BASE_URL}/api/completion', headers=headers, json=data)
    return response.json()
```

## Error Handling Examples

### Handling Validation Errors

```bash
# This will return a validation error
curl -X POST "https://localhost:7289/api/chat" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [],
    "maxTokens": -1,
    "temperature": 3.0
  }'
```

Expected response:
```json
{
  "message": "Validation failed",
  "errors": [
    {
      "field": "Messages",
      "error": "Messages are required"
    },
    {
      "field": "MaxTokens",
      "error": "MaxTokens must be greater than 0"
    },
    {
      "field": "Temperature",
      "error": "Temperature must be less than or equal to 2.0"
    }
  ]
}
```

### Handling Provider Errors

```bash
# This will return a provider not found error
curl -X GET "https://localhost:7289/api/providers/InvalidProvider/models" \
  -H "X-API-Key: your-api-key-here"
```

Expected response:
```json
{
  "provider": "InvalidProvider",
  "success": false,
  "errorMessage": "No models found for provider: InvalidProvider"
}
```

## Testing with Different Providers

### Claude Example

```bash
curl -X POST "https://localhost:7289/api/chat" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "Explain the concept of entropy in thermodynamics"
      }
    ],
    "maxTokens": 600,
    "temperature": 0.6,
    "provider": "Claude",
    "model": "claude-3-sonnet-20240229"
  }'
```

### Google Gemini Example

```bash
curl -X POST "https://localhost:7289/api/completion" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "The advantages of using renewable energy sources are",
    "maxTokens": 400,
    "temperature": 0.7,
    "provider": "Google",
    "model": "gemini-pro"
  }'
```

## Monitoring and Health Checks

### Basic Health Check

```bash
curl -X GET "https://localhost:7289/health"
```

### Detailed Health Check

```bash
curl -X GET "https://localhost:7289/api/health?includeProviderStatus=true&includeSystemInfo=true" \
  -H "X-API-Key: your-api-key-here"
```

This will return detailed information about:
- Service status and uptime
- Provider availability and response times
- System information (memory, CPU, etc.)
- Any warnings or issues
