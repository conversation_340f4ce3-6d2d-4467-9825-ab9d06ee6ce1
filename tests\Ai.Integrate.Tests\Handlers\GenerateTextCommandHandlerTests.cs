using Ai.Integrate.Application.Commands;
using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Handlers;
using Ai.Integrate.Application.Interfaces;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Ai.Integrate.Tests.Handlers;

public class GenerateTextCommandHandlerTests
{
    private readonly Mock<IAIService> _mockAIService;
    private readonly Mock<ILogger<GenerateTextCommandHandler>> _mockLogger;
    private readonly GenerateTextCommandHandler _handler;

    public GenerateTextCommandHandlerTests()
    {
        _mockAIService = new Mock<IAIService>();
        _mockLogger = new Mock<ILogger<GenerateTextCommandHandler>>();
        
        var aiServices = new List<IAIService> { _mockAIService.Object };
        _handler = new GenerateTextCommandHandler(aiServices, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var command = new GenerateTextCommand
        {
            Prompt = "Test prompt",
            MaxTokens = 100,
            Temperature = 0.7,
            Provider = "TestProvider"
        };

        var expectedResponse = new GenerateResponse
        {
            Text = "Generated text",
            Provider = "TestProvider",
            Success = true
        };

        _mockAIService.Setup(x => x.ProviderName).Returns("TestProvider");
        _mockAIService.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mockAIService.Setup(x => x.GenerateTextAsync(It.IsAny<GenerateRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal("Generated text", result.Text);
        Assert.Equal("TestProvider", result.Provider);
    }

    [Fact]
    public async Task Handle_ProviderNotAvailable_ReturnsErrorResponse()
    {
        // Arrange
        var command = new GenerateTextCommand
        {
            Prompt = "Test prompt",
            Provider = "TestProvider"
        };

        _mockAIService.Setup(x => x.ProviderName).Returns("TestProvider");
        _mockAIService.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("not available", result.ErrorMessage);
    }

    [Fact]
    public async Task Handle_NoProviderSpecified_UsesFirstAvailableProvider()
    {
        // Arrange
        var command = new GenerateTextCommand
        {
            Prompt = "Test prompt",
            Provider = null // No provider specified
        };

        var expectedResponse = new GenerateResponse
        {
            Text = "Generated text",
            Success = true
        };

        _mockAIService.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mockAIService.Setup(x => x.GenerateTextAsync(It.IsAny<GenerateRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal("Generated text", result.Text);
    }

    [Fact]
    public async Task Handle_ProviderNotFound_ReturnsErrorResponse()
    {
        // Arrange
        var command = new GenerateTextCommand
        {
            Prompt = "Test prompt",
            Provider = "NonExistentProvider"
        };

        _mockAIService.Setup(x => x.ProviderName).Returns("DifferentProvider");

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("No AI service found", result.ErrorMessage);
    }

    [Fact]
    public async Task Handle_ExceptionThrown_ReturnsErrorResponse()
    {
        // Arrange
        var command = new GenerateTextCommand
        {
            Prompt = "Test prompt",
            Provider = "TestProvider"
        };

        _mockAIService.Setup(x => x.ProviderName).Returns("TestProvider");
        _mockAIService.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("An error occurred during text generation", result.ErrorMessage);
    }
}
