using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace Ai.Integrate.Infrastructure.Caches;

/// <summary>
/// In-memory cache service implementation
/// </summary>
public class MemoryCache(IMemoryCache cache, ILogger<MemoryCache> logger)
{
    private readonly IMemoryCache _cache = cache ?? throw new ArgumentNullException(nameof(cache));
    private readonly ILogger<MemoryCache> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    private readonly HashSet<string> _keys = new();
    private readonly object _keysLock = new();

    private Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_cache.TryGetValue(key, out var value))
            {
                if (value is T typedValue)
                {
                    _logger.LogDebug("Cache hit for key: {Key}", key);
                    return Task.FromResult<T?>(typedValue);
                }

                if (value is string jsonValue && typeof(T) != typeof(string))
                {
                    var deserializedValue = JsonSerializer.Deserialize<T>(jsonValue);
                    _logger.LogDebug("Cache hit (deserialized) for key: {Key}", key);
                    return Task.FromResult(deserializedValue);
                }
            }

            _logger.LogDebug("Cache miss for key: {Key}", key);
            return Task.FromResult<T?>(default);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cached value for key: {Key}", key);
            return Task.FromResult<T?>(default);
        }
    }

    private Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiration = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var options = new MemoryCacheEntryOptions();

            if (expiration.HasValue)
            {
                options.AbsoluteExpirationRelativeToNow = expiration.Value;
            }
            else
            {
                // Default expiration of 1 hour
                options.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
            }

            // Add eviction callback to remove key from tracking
            options.RegisterPostEvictionCallback((k, v, reason, state) =>
            {
                lock (_keysLock)
                {
                    _keys.Remove(k.ToString() ?? string.Empty);
                }

                _logger.LogDebug("Cache entry evicted: {Key}, Reason: {Reason}", k, reason);
            });

            object cacheValue = value;

            // Serialize complex objects to JSON for better memory management
            if (typeof(T) != typeof(string) && typeof(T) != typeof(int) && typeof(T) != typeof(bool) &&
                typeof(T) != typeof(double) && typeof(T) != typeof(decimal) && typeof(T) != typeof(DateTime))
            {
                cacheValue = JsonSerializer.Serialize(value);
            }

            _cache.Set(key, cacheValue, options);

            lock (_keysLock)
            {
                _keys.Add(key);
            }

            _logger.LogDebug("Cached value for key: {Key}, Expiration: {Expiration}", key, expiration);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
            return Task.FromResult(false);
        }
    }

    public Task<bool> RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            _cache.Remove(key);

            lock (_keysLock)
            {
                _keys.Remove(key);
            }

            _logger.LogDebug("Removed cached value for key: {Key}", key);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
            return Task.FromResult(false);
        }
    }

    public Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var exists = _cache.TryGetValue(key, out _);
            return Task.FromResult(exists);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if key exists: {Key}", key);
            return Task.FromResult(false);
        }
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null,
        CancellationToken cancellationToken = default)
    {
        var cachedValue = await GetAsync<T>(key, cancellationToken);

        if (cachedValue != null)
        {
            return cachedValue;
        }

        try
        {
            var value = await factory();
            await SetAsync(key, value, expiration, cancellationToken);
            return value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetOrSetAsync for key: {Key}", key);
            throw;
        }
    }

    public Task<int> RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        try
        {
            var regex = new Regex(pattern.Replace("*", ".*"), RegexOptions.IgnoreCase);
            var keysToRemove = new List<string>();

            lock (_keysLock)
            {
                keysToRemove.AddRange(_keys.Where(key => regex.IsMatch(key)));
            }

            foreach (var key in keysToRemove)
            {
                _cache.Remove(key);
                lock (_keysLock)
                {
                    _keys.Remove(key);
                }
            }

            _logger.LogDebug("Removed {Count} cached values matching pattern: {Pattern}", keysToRemove.Count, pattern);
            return Task.FromResult(keysToRemove.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached values by pattern: {Pattern}", pattern);
            return Task.FromResult(0);
        }
    }
}