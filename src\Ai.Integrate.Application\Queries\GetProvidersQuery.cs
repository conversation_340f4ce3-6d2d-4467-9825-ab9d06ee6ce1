using Ai.Integrate.Application.Dtos;
using MediatR;

namespace Ai.Integrate.Application.Queries;

/// <summary>
/// Query to get all available AI providers
/// </summary>
public class GetProvidersQuery : IRequest<ProvidersResponse>
{
    /// <summary>
    /// Whether to include only enabled providers
    /// </summary>
    public bool EnabledOnly { get; set; } = false;
    
    /// <summary>
    /// Whether to check provider availability status
    /// </summary>
    public bool CheckAvailability { get; set; } = true;
}
