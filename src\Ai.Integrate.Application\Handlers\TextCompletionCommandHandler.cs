using Ai.Integrate.Application.Commands;
using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Ai.Integrate.Application.Handlers;

/// <summary>
/// Handler for TextCompletionCommand
/// </summary>
public class TextCompletionCommandHandler : IRequestHandler<TextCompletionCommand, CompletionResponse>
{
    private readonly IEnumerable<IAIService> _aiServices;
    private readonly ILogger<TextCompletionCommandHandler> _logger;

    public TextCompletionCommandHandler(
        IEnumerable<IAIService> aiServices,
        ILogger<TextCompletionCommandHandler> logger)
    {
        _aiServices = aiServices ?? throw new ArgumentNullException(nameof(aiServices));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<CompletionResponse> Handle(TextCompletionCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling TextCompletionCommand with provider: {Provider}", request.Provider ?? "default");

        try
        {
            var aiService = GetAIService(request.Provider);
            
            if (aiService == null)
            {
                _logger.LogError("No AI service found for provider: {Provider}", request.Provider ?? "default");
                return new CompletionResponse
                {
                    Success = false,
                    ErrorMessage = $"No AI service found for provider: {request.Provider ?? "default"}"
                };
            }

            // Check if the service is available
            var isAvailable = await aiService.IsAvailableAsync(cancellationToken);
            if (!isAvailable)
            {
                _logger.LogWarning("AI service {Provider} is not available", aiService.ProviderName);
                return new CompletionResponse
                {
                    Success = false,
                    ErrorMessage = $"AI service {aiService.ProviderName} is not available"
                };
            }

            var choices = new List<CompletionChoice>();
            var totalPromptTokens = 0;
            var totalCompletionTokens = 0;
            var startTime = DateTime.UtcNow;

            // Generate multiple completions if requested
            for (int i = 0; i < request.NumberOfCompletions; i++)
            {
                var generateRequest = new GenerateRequest
                {
                    Prompt = request.Prompt,
                    MaxTokens = request.MaxTokens,
                    Temperature = request.Temperature,
                    TopP = request.TopP,
                    Provider = request.Provider,
                    Model = request.Model
                };

                var generateResponse = await aiService.GenerateTextAsync(generateRequest, cancellationToken);
                
                if (!generateResponse.Success)
                {
                    return new CompletionResponse
                    {
                        Success = false,
                        ErrorMessage = generateResponse.ErrorMessage,
                        Provider = generateResponse.Provider
                    };
                }

                var text = generateResponse.Text;
                
                // Apply echo if requested
                if (request.Echo)
                {
                    text = request.Prompt + text;
                }

                // Apply stop sequences
                if (request.StopSequences != null && request.StopSequences.Any())
                {
                    foreach (var stopSequence in request.StopSequences)
                    {
                        var stopIndex = text.IndexOf(stopSequence, StringComparison.OrdinalIgnoreCase);
                        if (stopIndex >= 0)
                        {
                            text = text.Substring(0, stopIndex);
                            break;
                        }
                    }
                }

                choices.Add(new CompletionChoice
                {
                    Text = text,
                    Index = i,
                    FinishReason = "stop" // Simplified for now
                });

                totalPromptTokens += generateResponse.PromptTokens;
                totalCompletionTokens += generateResponse.CompletionTokens;
            }

            var endTime = DateTime.UtcNow;
            
            var response = new CompletionResponse
            {
                Choices = choices,
                Provider = aiService.ProviderName,
                Model = request.Model ?? "default",
                PromptTokens = totalPromptTokens,
                CompletionTokens = totalCompletionTokens,
                TotalTokens = totalPromptTokens + totalCompletionTokens,
                Duration = endTime - startTime,
                Timestamp = endTime,
                Success = true
            };
            
            _logger.LogInformation("Text completion completed successfully in {Duration}ms", 
                response.Duration.TotalMilliseconds);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during text completion");
            return new CompletionResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred during text completion: {ex.Message}"
            };
        }
    }

    private IAIService? GetAIService(string? providerName)
    {
        if (string.IsNullOrEmpty(providerName))
        {
            return _aiServices.FirstOrDefault();
        }

        return _aiServices.FirstOrDefault(s => 
            string.Equals(s.ProviderName, providerName, StringComparison.OrdinalIgnoreCase));
    }
}
