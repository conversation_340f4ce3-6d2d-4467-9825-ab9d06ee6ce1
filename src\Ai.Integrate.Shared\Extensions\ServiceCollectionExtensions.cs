using Ai.Integrate.Application.Interfaces;
using Ai.Integrate.Application.UseCases;
using Ai.Integrate.Infrastructure.Config;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.CircuitBreaker;
using Polly.Extensions.Http;
using RestSharp;

namespace Ai.Integrate.Shared.Extensions;

/// <summary>
/// Extension methods for IServiceCollection
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Add application services
    /// </summary>
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // Register MediatR
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(GenerateTextUseCase).Assembly));

        // Register use cases (keeping for backward compatibility)
        services.AddScoped<IGenerateTextUseCase, GenerateTextUseCase>();

        return services;
    }

    /// <summary>
    /// Add HTTP clients with Polly policies
    /// </summary>
    public static IServiceCollection AddHttpClientsWithPolly(this IServiceCollection services, IConfiguration configuration)
    {
        var aiSettings = configuration.GetSection(AISettings.SectionName).Get<AISettings>() ?? new AISettings();
        var httpSettings = aiSettings.HttpClient;

        // Configure retry policy
        var retryPolicy = HttpPolicyExtensions
            .HandleTransientHttpError()
            .WaitAndRetryAsync(
                retryCount: httpSettings.RetryCount,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt) * httpSettings.RetryDelaySeconds),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    // Log retry attempts - you can inject ILogger here if needed
                    Console.WriteLine($"Retry {retryCount} for {context.OperationKey} in {timespan.TotalMilliseconds}ms");
                });

        // Configure circuit breaker policy
        var circuitBreakerPolicy = HttpPolicyExtensions
            .HandleTransientHttpError()
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: httpSettings.CircuitBreakerFailureThreshold,
                durationOfBreak: TimeSpan.FromSeconds(httpSettings.CircuitBreakerTimeoutSeconds),
                onBreak: (exception, duration) =>
                {
                    // Log circuit breaker opening
                },
                onReset: () =>
                {
                    // Log circuit breaker closing
                });

        // Combine policies
        var combinedPolicy = Policy.WrapAsync(retryPolicy, circuitBreakerPolicy);

        // Register HttpClient for each AI provider
        services.AddHttpClient("OpenAI", client =>
        {
            client.BaseAddress = new Uri(aiSettings.OpenAI.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(httpSettings.TimeoutSeconds);
            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {aiSettings.OpenAI.ApiKey}");
        }).AddPolicyHandler(combinedPolicy);

        services.AddHttpClient("Claude", client =>
        {
            client.BaseAddress = new Uri(aiSettings.Claude.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(httpSettings.TimeoutSeconds);
            client.DefaultRequestHeaders.Add("x-api-key", aiSettings.Claude.ApiKey);
            client.DefaultRequestHeaders.Add("anthropic-version", "2023-06-01");
        }).AddPolicyHandler(combinedPolicy);

        services.AddHttpClient("Google", client =>
        {
            client.BaseAddress = new Uri(aiSettings.Google.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(httpSettings.TimeoutSeconds);
        }).AddPolicyHandler(combinedPolicy);

        services.AddHttpClient("OpenRouter", client =>
        {
            client.BaseAddress = new Uri(aiSettings.OpenRouter.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(httpSettings.TimeoutSeconds);
            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {aiSettings.OpenRouter.ApiKey}");
        }).AddPolicyHandler(combinedPolicy);

        services.AddHttpClient("Llama", client =>
        {
            client.BaseAddress = new Uri(aiSettings.Llama.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(httpSettings.TimeoutSeconds);
        }).AddPolicyHandler(combinedPolicy);

        services.AddHttpClient("LangChain", client =>
        {
            client.BaseAddress = new Uri(aiSettings.LangChain.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(httpSettings.TimeoutSeconds);
            if (!string.IsNullOrEmpty(aiSettings.LangChain.ApiKey))
            {
                client.DefaultRequestHeaders.Add("Authorization", $"Bearer {aiSettings.LangChain.ApiKey}");
            }
        }).AddPolicyHandler(combinedPolicy);

        return services;
    }

    /// <summary>
    /// Add RestSharp clients with Polly policies
    /// </summary>
    public static IServiceCollection AddRestSharpClients(this IServiceCollection services, IConfiguration configuration)
    {
        var aiSettings = configuration.GetSection(AISettings.SectionName).Get<AISettings>() ?? new AISettings();

        // Register RestSharp clients for each provider
        services.AddSingleton<IRestClient>(provider =>
        {
            var options = new RestClientOptions(aiSettings.OpenAI.BaseUrl)
            {
                Timeout = TimeSpan.FromSeconds(aiSettings.HttpClient.TimeoutSeconds)
            };
            return new RestClient(options);
        });

        return services;
    }

    /// <summary>
    /// Add configuration settings
    /// </summary>
    public static IServiceCollection AddConfigurationSettings(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<AISettings>(configuration.GetSection(AISettings.SectionName));

        return services;
    }
}
