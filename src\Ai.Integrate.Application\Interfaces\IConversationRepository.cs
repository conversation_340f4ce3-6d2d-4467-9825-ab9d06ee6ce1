using Ai.Integrate.Application.Dtos;

namespace Ai.Integrate.Application.Interfaces;

/// <summary>
/// Repository interface for conversation management
/// </summary>
public interface IConversationRepository
{
    /// <summary>
    /// Get conversation by ID
    /// </summary>
    /// <param name="conversationId">Conversation identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Conversation or null if not found</returns>
    Task<Conversation?> GetConversationAsync(string conversationId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Create a new conversation
    /// </summary>
    /// <param name="conversation">Conversation to create</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created conversation</returns>
    Task<Conversation> CreateConversationAsync(Conversation conversation, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Update existing conversation
    /// </summary>
    /// <param name="conversation">Conversation to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated conversation</returns>
    Task<Conversation> UpdateConversationAsync(Conversation conversation, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Delete conversation
    /// </summary>
    /// <param name="conversationId">Conversation identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteConversationAsync(string conversationId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get conversations for a user
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="pageSize">Number of conversations per page</param>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of conversations</returns>
    Task<PaginatedResult<Conversation>> GetUserConversationsAsync(
        string userId, 
        int pageSize = 20, 
        int pageNumber = 1, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Add message to conversation
    /// </summary>
    /// <param name="conversationId">Conversation identifier</param>
    /// <param name="message">Message to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if added successfully</returns>
    Task<bool> AddMessageAsync(string conversationId, ChatMessage message, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get conversation messages
    /// </summary>
    /// <param name="conversationId">Conversation identifier</param>
    /// <param name="limit">Maximum number of messages to return</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of messages</returns>
    Task<List<ChatMessage>> GetMessagesAsync(string conversationId, int limit = 100, CancellationToken cancellationToken = default);
}

/// <summary>
/// Conversation entity
/// </summary>
public class Conversation
{
    /// <summary>
    /// Conversation identifier
    /// </summary>
    public string Id { get; set; } = string.Empty;
    
    /// <summary>
    /// User identifier
    /// </summary>
    public string UserId { get; set; } = string.Empty;
    
    /// <summary>
    /// Conversation title
    /// </summary>
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// AI provider used
    /// </summary>
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// Model used
    /// </summary>
    public string Model { get; set; } = string.Empty;
    
    /// <summary>
    /// Conversation messages
    /// </summary>
    public List<ChatMessage> Messages { get; set; } = new();
    
    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Whether the conversation is active
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// Conversation metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Paginated result wrapper
/// </summary>
/// <typeparam name="T">Type of items</typeparam>
public class PaginatedResult<T>
{
    /// <summary>
    /// List of items
    /// </summary>
    public List<T> Items { get; set; } = new();
    
    /// <summary>
    /// Total number of items
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// Current page number (1-based)
    /// </summary>
    public int PageNumber { get; set; }
    
    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; }
    
    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    
    /// <summary>
    /// Whether there is a next page
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;
    
    /// <summary>
    /// Whether there is a previous page
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;
}
