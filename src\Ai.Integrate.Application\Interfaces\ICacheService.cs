namespace Ai.Integrate.Application.Interfaces;

/// <summary>
/// Interface for caching service
/// </summary>
public interface ICacheService
{
    /// <summary>
    /// Get cached value
    /// </summary>
    /// <typeparam name="T">Type of cached value</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached value or default</returns>
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Set cached value
    /// </summary>
    /// <typeparam name="T">Type of value to cache</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="value">Value to cache</param>
    /// <param name="expiration">Cache expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if cached successfully</returns>
    Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Remove cached value
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if removed successfully</returns>
    Task<bool> RemoveAsync(string key, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Check if key exists in cache
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if key exists</returns>
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get or set cached value
    /// </summary>
    /// <typeparam name="T">Type of value</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="factory">Factory function to create value if not cached</param>
    /// <param name="expiration">Cache expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached or newly created value</returns>
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Remove all cached values matching pattern
    /// </summary>
    /// <param name="pattern">Pattern to match (supports wildcards)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of keys removed</returns>
    Task<int> RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
}

/// <summary>
/// Cache key builder for consistent key generation
/// </summary>
public static class CacheKeys
{
    private const string Separator = ":";
    
    /// <summary>
    /// Generate cache key for provider availability
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <returns>Cache key</returns>
    public static string ProviderAvailability(string providerName) => 
        $"provider{Separator}availability{Separator}{providerName.ToLowerInvariant()}";
    
    /// <summary>
    /// Generate cache key for provider models
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <returns>Cache key</returns>
    public static string ProviderModels(string providerName) => 
        $"provider{Separator}models{Separator}{providerName.ToLowerInvariant()}";
    
    /// <summary>
    /// Generate cache key for model information
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="modelId">Model identifier</param>
    /// <returns>Cache key</returns>
    public static string ModelInfo(string providerName, string modelId) => 
        $"model{Separator}info{Separator}{providerName.ToLowerInvariant()}{Separator}{modelId.ToLowerInvariant()}";
    
    /// <summary>
    /// Generate cache key for conversation
    /// </summary>
    /// <param name="conversationId">Conversation identifier</param>
    /// <returns>Cache key</returns>
    public static string Conversation(string conversationId) => 
        $"conversation{Separator}{conversationId}";
    
    /// <summary>
    /// Generate cache key for user conversations
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Cache key</returns>
    public static string UserConversations(string userId, int pageNumber, int pageSize) => 
        $"user{Separator}conversations{Separator}{userId}{Separator}{pageNumber}{Separator}{pageSize}";
    
    /// <summary>
    /// Generate cache key for health status
    /// </summary>
    /// <returns>Cache key</returns>
    public static string HealthStatus() => "health{Separator}status";
    
    /// <summary>
    /// Generate cache key for system info
    /// </summary>
    /// <returns>Cache key</returns>
    public static string SystemInfo() => "system{Separator}info";
}
