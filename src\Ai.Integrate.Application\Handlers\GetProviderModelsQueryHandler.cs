using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using Ai.Integrate.Application.Queries;
using Ai.Integrate.Infrastructure.Config;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Ai.Integrate.Application.Handlers;

/// <summary>
/// Handler for GetProviderModelsQuery
/// </summary>
public class GetProviderModelsQueryHandler : IRequestHandler<GetProviderModelsQuery, ModelsResponse>
{
    private readonly IEnumerable<IAIService> _aiServices;
    private readonly AISettings _settings;
    private readonly ILogger<GetProviderModelsQueryHandler> _logger;

    public GetProviderModelsQueryHandler(
        IEnumerable<IAIService> aiServices,
        IOptions<AISettings> settings,
        ILogger<GetProviderModelsQueryHandler> logger)
    {
        _aiServices = aiServices ?? throw new ArgumentNullException(nameof(aiServices));
        _settings = settings.Value ?? throw new ArgumentNullException(nameof(settings));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ModelsResponse> Handle(GetProviderModelsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling GetProviderModelsQuery for provider: {Provider}", request.Provider);

        try
        {
            var models = GetModelsForProvider(request.Provider, request.EnabledOnly, request.IncludeCapabilities);
            
            if (!models.Any())
            {
                return new ModelsResponse
                {
                    Provider = request.Provider,
                    Success = false,
                    ErrorMessage = $"No models found for provider: {request.Provider}"
                };
            }

            var defaultModel = GetDefaultModelForProvider(request.Provider);

            var response = new ModelsResponse
            {
                Provider = request.Provider,
                Models = models,
                DefaultModel = defaultModel,
                Success = true
            };

            _logger.LogInformation("Successfully retrieved {Count} models for provider {Provider}", 
                models.Count, request.Provider);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting models for provider {Provider}", request.Provider);
            return new ModelsResponse
            {
                Provider = request.Provider,
                Success = false,
                ErrorMessage = $"An error occurred while getting models: {ex.Message}"
            };
        }
    }

    private List<ModelInfo> GetModelsForProvider(string provider, bool enabledOnly, bool includeCapabilities)
    {
        var models = new List<ModelInfo>();

        switch (provider.ToLowerInvariant())
        {
            case "openai":
                models.AddRange(GetOpenAIModels(enabledOnly, includeCapabilities));
                break;
            case "claude":
                models.AddRange(GetClaudeModels(enabledOnly, includeCapabilities));
                break;
            case "google":
                models.AddRange(GetGoogleModels(enabledOnly, includeCapabilities));
                break;
            case "openrouter":
                models.AddRange(GetOpenRouterModels(enabledOnly, includeCapabilities));
                break;
            case "llama":
                models.AddRange(GetLlamaModels(enabledOnly, includeCapabilities));
                break;
            case "langchain":
                models.AddRange(GetLangChainModels(enabledOnly, includeCapabilities));
                break;
        }

        return models;
    }

    private List<ModelInfo> GetOpenAIModels(bool enabledOnly, bool includeCapabilities)
    {
        var models = new List<ModelInfo>
        {
            new() { Id = "gpt-4", Name = "GPT-4", Description = "Most capable GPT-4 model", MaxTokens = 8192, Type = "chat", Version = "2023-03-15", IsDefault = false, Enabled = true },
            new() { Id = "gpt-4-turbo", Name = "GPT-4 Turbo", Description = "Latest GPT-4 Turbo model", MaxTokens = 128000, Type = "chat", Version = "2024-04-09", IsDefault = false, Enabled = true },
            new() { Id = "gpt-3.5-turbo", Name = "GPT-3.5 Turbo", Description = "Fast and efficient model", MaxTokens = 4096, Type = "chat", Version = "2023-06-13", IsDefault = true, Enabled = true },
            new() { Id = "gpt-3.5-turbo-16k", Name = "GPT-3.5 Turbo 16K", Description = "Extended context version", MaxTokens = 16384, Type = "chat", Version = "2023-06-13", IsDefault = false, Enabled = true },
            new() { Id = "text-davinci-003", Name = "Davinci 003", Description = "Legacy completion model", MaxTokens = 4097, Type = "completion", Version = "2022-11-28", IsDefault = false, Enabled = false }
        };

        if (includeCapabilities)
        {
            foreach (var model in models)
            {
                model.Capabilities = model.Type == "chat" 
                    ? new List<string> { "chat", "completion", "streaming", "function-calling" }
                    : new List<string> { "completion", "streaming" };
            }
        }

        return enabledOnly ? models.Where(m => m.Enabled).ToList() : models;
    }

    private List<ModelInfo> GetClaudeModels(bool enabledOnly, bool includeCapabilities)
    {
        var models = new List<ModelInfo>
        {
            new() { Id = "claude-3-opus-20240229", Name = "Claude 3 Opus", Description = "Most powerful Claude model", MaxTokens = 200000, Type = "chat", Version = "2024-02-29", IsDefault = false, Enabled = true },
            new() { Id = "claude-3-sonnet-20240229", Name = "Claude 3 Sonnet", Description = "Balanced performance and speed", MaxTokens = 200000, Type = "chat", Version = "2024-02-29", IsDefault = true, Enabled = true },
            new() { Id = "claude-3-haiku-20240307", Name = "Claude 3 Haiku", Description = "Fastest Claude model", MaxTokens = 200000, Type = "chat", Version = "2024-03-07", IsDefault = false, Enabled = true }
        };

        if (includeCapabilities)
        {
            foreach (var model in models)
            {
                model.Capabilities = new List<string> { "chat", "completion", "streaming", "vision", "document-analysis" };
            }
        }

        return enabledOnly ? models.Where(m => m.Enabled).ToList() : models;
    }

    private List<ModelInfo> GetGoogleModels(bool enabledOnly, bool includeCapabilities)
    {
        var models = new List<ModelInfo>
        {
            new() { Id = "gemini-pro", Name = "Gemini Pro", Description = "Google's most capable model", MaxTokens = 32768, Type = "chat", Version = "1.0", IsDefault = true, Enabled = true },
            new() { Id = "gemini-pro-vision", Name = "Gemini Pro Vision", Description = "Multimodal model with vision", MaxTokens = 16384, Type = "chat", Version = "1.0", IsDefault = false, Enabled = true },
            new() { Id = "gemini-ultra", Name = "Gemini Ultra", Description = "Most capable Gemini model", MaxTokens = 32768, Type = "chat", Version = "1.0", IsDefault = false, Enabled = false }
        };

        if (includeCapabilities)
        {
            foreach (var model in models)
            {
                model.Capabilities = model.Id.Contains("vision") 
                    ? new List<string> { "chat", "completion", "vision", "multimodal" }
                    : new List<string> { "chat", "completion", "streaming" };
            }
        }

        return enabledOnly ? models.Where(m => m.Enabled).ToList() : models;
    }

    private List<ModelInfo> GetOpenRouterModels(bool enabledOnly, bool includeCapabilities)
    {
        // This would typically be fetched from OpenRouter's API
        var models = new List<ModelInfo>
        {
            new() { Id = "openai/gpt-4", Name = "GPT-4 (OpenRouter)", Description = "GPT-4 via OpenRouter", MaxTokens = 8192, Type = "chat", Version = "2023-03-15", IsDefault = false, Enabled = true },
            new() { Id = "openai/gpt-3.5-turbo", Name = "GPT-3.5 Turbo (OpenRouter)", Description = "GPT-3.5 via OpenRouter", MaxTokens = 4096, Type = "chat", Version = "2023-06-13", IsDefault = true, Enabled = true },
            new() { Id = "anthropic/claude-2", Name = "Claude 2 (OpenRouter)", Description = "Claude 2 via OpenRouter", MaxTokens = 100000, Type = "chat", Version = "2023-07-11", IsDefault = false, Enabled = true }
        };

        if (includeCapabilities)
        {
            foreach (var model in models)
            {
                model.Capabilities = new List<string> { "chat", "completion", "streaming" };
            }
        }

        return enabledOnly ? models.Where(m => m.Enabled).ToList() : models;
    }

    private List<ModelInfo> GetLlamaModels(bool enabledOnly, bool includeCapabilities)
    {
        var models = new List<ModelInfo>
        {
            new() { Id = "llama2", Name = "Llama 2", Description = "Meta's Llama 2 model", MaxTokens = 4096, Type = "completion", Version = "2.0", IsDefault = true, Enabled = true },
            new() { Id = "llama2:13b", Name = "Llama 2 13B", Description = "Larger Llama 2 model", MaxTokens = 4096, Type = "completion", Version = "2.0", IsDefault = false, Enabled = true },
            new() { Id = "codellama", Name = "Code Llama", Description = "Code-specialized Llama model", MaxTokens = 4096, Type = "completion", Version = "1.0", IsDefault = false, Enabled = true }
        };

        if (includeCapabilities)
        {
            foreach (var model in models)
            {
                model.Capabilities = model.Id.Contains("code") 
                    ? new List<string> { "completion", "code-generation", "streaming" }
                    : new List<string> { "completion", "streaming" };
            }
        }

        return enabledOnly ? models.Where(m => m.Enabled).ToList() : models;
    }

    private List<ModelInfo> GetLangChainModels(bool enabledOnly, bool includeCapabilities)
    {
        var models = new List<ModelInfo>
        {
            new() { Id = "langchain-default", Name = "LangChain Default", Description = "Default LangChain model", MaxTokens = 4096, Type = "completion", Version = "1.0", IsDefault = true, Enabled = true }
        };

        if (includeCapabilities)
        {
            foreach (var model in models)
            {
                model.Capabilities = new List<string> { "completion", "chain-of-thought", "tool-use" };
            }
        }

        return enabledOnly ? models.Where(m => m.Enabled).ToList() : models;
    }

    private string GetDefaultModelForProvider(string provider)
    {
        return provider.ToLowerInvariant() switch
        {
            "openai" => _settings.OpenAI.DefaultModel,
            "claude" => _settings.Claude.DefaultModel,
            "google" => _settings.Google.DefaultModel,
            "openrouter" => _settings.OpenRouter.DefaultModel,
            "llama" => _settings.Llama.DefaultModel,
            "langchain" => "langchain-default",
            _ => string.Empty
        };
    }
}
