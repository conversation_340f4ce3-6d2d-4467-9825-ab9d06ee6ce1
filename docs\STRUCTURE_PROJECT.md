﻿/src
├── Api                      # Minimal API Layer (Presentation)
│    ├── Program.cs          # Entry point, DI, routing
│    ├── appsettings.json
│    └── Controllers         # Nếu cần controller truyền thống
│
├── Application              # Application Layer (Use Cases)
│    ├── Interfaces          # Các contract/port do API và Domain dùng
│    │    └── IAIService.cs
│    ├── DTOs                # Data Transfer Objects
│    │    └── GenerateRequest.cs
│    │    └── GenerateResponse.cs
│    ├── UseCases            # Các business logic cụ thể
│    │    └── GenerateTextUseCase.cs
│    └── Validators          # (Tuỳ chọn) FluentValidation, etc.
│
├── Domain                   # Domain Layer (Entities & Value Objects)
│    ├── Entities            # Entity core (nếu có)
│    └── ValueObjects        # VO (nếu có)
│
├── Infrastructure           # Infrastructure Layer (Adapters)
│    ├── AIProviders         # Implementations của IAIService
│    │    ├── OpenAIProvider.cs
│    │    ├── ClaudeProvider.cs
│    │    └── GoogleProvider.cs
│    ├── HttpClients         # Config HttpClientFactory, Polly policies
│    └── Config              # Bản đồ cấu hình từ appsettings
│         └── AISettings.cs
│
└── Shared                   # Common helpers, extensions, logging, middleware
├── Extensions          # e.g. IServiceCollectionExtensions.cs
└── Middleware          # e.g. ApiKeyAuthMiddleware.cs

/tests
├── Api.Tests                # Integration tests cho endpoints
├── Application.Tests        # Unit tests cho UseCases
└── Infrastructure.Tests     # Mocks & tests cho provider adapters
