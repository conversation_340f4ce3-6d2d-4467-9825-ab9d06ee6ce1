using Ai.Integrate.Shared.Middleware;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text;
using Xunit;

namespace Ai.Integrate.Tests.Middleware;

public class RateLimitingMiddlewareTests
{
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<RateLimitingMiddleware>> _mockLogger;
    private readonly Mock<RequestDelegate> _mockNext;

    public RateLimitingMiddlewareTests()
    {
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<RateLimitingMiddleware>>();
        _mockNext = new Mock<RequestDelegate>();

        // Setup default configuration
        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:AI:RequestsPerMinute", 60)).Returns(2);
        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:AI:RequestsPerHour", 1000)).Returns(10);
        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:AI:BurstLimit", 10)).Returns(5);
    }

    [Fact]
    public async Task InvokeAsync_HealthCheckPath_SkipsRateLimit()
    {
        // Arrange
        var middleware = new RateLimitingMiddleware(_mockNext.Object, _mockConfiguration.Object, _mockLogger.Object);
        var context = CreateHttpContext("/health");

        // Act
        await middleware.InvokeAsync(context);

        // Assert
        _mockNext.Verify(x => x(context), Times.Once);
        Assert.Equal(200, context.Response.StatusCode);
    }

    [Fact]
    public async Task InvokeAsync_FirstRequest_AllowsRequest()
    {
        // Arrange
        var middleware = new RateLimitingMiddleware(_mockNext.Object, _mockConfiguration.Object, _mockLogger.Object);
        var context = CreateHttpContext("/api/generate");
        context.Request.Headers.Add("X-API-Key", "test-key");

        // Act
        await middleware.InvokeAsync(context);

        // Assert
        _mockNext.Verify(x => x(context), Times.Once);
        Assert.True(context.Response.Headers.ContainsKey("X-RateLimit-Limit-Minute"));
    }

    [Fact]
    public async Task InvokeAsync_ExceedsRateLimit_Returns429()
    {
        // Arrange
        var middleware = new RateLimitingMiddleware(_mockNext.Object, _mockConfiguration.Object, _mockLogger.Object);
        var context = CreateHttpContext("/api/generate");
        context.Request.Headers.Add("X-API-Key", "test-key");

        // Act - Make multiple requests to exceed rate limit
        await middleware.InvokeAsync(context);
        await middleware.InvokeAsync(context);
        
        // Reset context for third request
        context = CreateHttpContext("/api/generate");
        context.Request.Headers.Add("X-API-Key", "test-key");
        await middleware.InvokeAsync(context);

        // Assert
        Assert.Equal(429, context.Response.StatusCode);
        Assert.True(context.Response.Headers.ContainsKey("Retry-After"));
    }

    [Fact]
    public async Task InvokeAsync_DifferentApiKeys_SeparateRateLimits()
    {
        // Arrange
        var middleware = new RateLimitingMiddleware(_mockNext.Object, _mockConfiguration.Object, _mockLogger.Object);
        
        var context1 = CreateHttpContext("/api/generate");
        context1.Request.Headers.Add("X-API-Key", "key1");
        
        var context2 = CreateHttpContext("/api/generate");
        context2.Request.Headers.Add("X-API-Key", "key2");

        // Act
        await middleware.InvokeAsync(context1);
        await middleware.InvokeAsync(context1); // Should still work for key1
        await middleware.InvokeAsync(context2); // Should work for key2

        // Assert
        _mockNext.Verify(x => x(It.IsAny<HttpContext>()), Times.Exactly(3));
    }

    private static HttpContext CreateHttpContext(string path)
    {
        var context = new DefaultHttpContext();
        context.Request.Path = path;
        context.Request.Method = "POST";
        context.Response.Body = new MemoryStream();
        return context;
    }
}
