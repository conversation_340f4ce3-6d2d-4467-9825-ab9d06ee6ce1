using System.Text.Json.Serialization;

namespace Ai.Integrate.Application.Dtos;

/// <summary>
/// Response model for the OpenAI models endpoint
/// </summary>
public class OpenAIModelsResponse
{
    /// <summary>
    /// The object type, typically "list"
    /// </summary>
    [JsonPropertyName("object")]
    public string Object { get; set; } = string.Empty;

    /// <summary>
    /// The list of models returned from the OpenAI API
    /// </summary>
    [JsonPropertyName("data")]
    public List<OpenAIModel> Data { get; set; } = new();
}

/// <summary>
/// Information about an OpenAI model
/// </summary>
public class OpenAIModel
{
    /// <summary>
    /// The model identifier
    /// </summary>
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// The object type, typically "model"
    /// </summary>
    [JsonPropertyName("object")]
    public string Object { get; set; } = string.Empty;

    /// <summary>
    /// Unix timestamp when the model was created
    /// </summary>
    [JsonPropertyName("created")]
    public long Created { get; set; }

    /// <summary>
    /// The owner of the model
    /// </summary>
    [JsonPropertyName("owned_by")]
    public string OwnedBy { get; set; } = string.Empty;

    /// <summary>
    /// Maximum tokens the model can handle in a single request
    /// </summary>
    [JsonPropertyName("context_window")]
    public int MaxTokens { get; set; }

    /// <summary>
    /// Permission details for the model
    /// </summary>
    [JsonPropertyName("permission")]
    public List<OpenAIPermission>? Permission { get; set; }
}

/// <summary>
/// Permission details for an OpenAI model
/// </summary>
public class OpenAIPermission
{
    /// <summary>
    /// The ID of the permission
    /// </summary>
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// The object type
    /// </summary>
    [JsonPropertyName("object")]
    public string Object { get; set; } = string.Empty;

    /// <summary>
    /// Unix timestamp when the permission was created
    /// </summary>
    [JsonPropertyName("created")]
    public long Created { get; set; }

    /// <summary>
    /// Whether the model can be used with this permission
    /// </summary>
    [JsonPropertyName("allow_create_engine")]
    public bool AllowCreateEngine { get; set; }

    /// <summary>
    /// Whether fine-tuning is allowed with this permission
    /// </summary>
    [JsonPropertyName("allow_fine_tuning")]
    public bool AllowFineTuning { get; set; }

    /// <summary>
    /// Whether model sampling is allowed with this permission
    /// </summary>
    [JsonPropertyName("allow_sampling")]
    public bool AllowSampling { get; set; }

    /// <summary>
    /// Whether logprobs are allowed with this permission
    /// </summary>
    [JsonPropertyName("allow_logprobs")]
    public bool AllowLogprobs { get; set; }

    /// <summary>
    /// Whether search indices are allowed with this permission
    /// </summary>
    [JsonPropertyName("allow_search_indices")]
    public bool AllowSearchIndices { get; set; }

    /// <summary>
    /// Whether view is allowed with this permission
    /// </summary>
    [JsonPropertyName("allow_view")]
    public bool AllowView { get; set; }

    /// <summary>
    /// Whether the model can be used to fulfill requests on this organization
    /// </summary>
    [JsonPropertyName("is_blocking")]
    public bool IsBlocking { get; set; }

    /// <summary>
    /// The organization ID that this permission applies to
    /// </summary>
    [JsonPropertyName("organization")]
    public string Organization { get; set; } = string.Empty;
}
