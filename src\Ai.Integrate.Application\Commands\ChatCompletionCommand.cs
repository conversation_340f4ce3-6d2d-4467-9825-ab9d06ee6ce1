using Ai.Integrate.Application.Dtos;
using MediatR;

namespace Ai.Integrate.Application.Commands;

/// <summary>
/// Command for chat completion with conversation history
/// </summary>
public class ChatCompletionCommand : IRequest<ChatResponse>
{
    /// <summary>
    /// List of messages in the conversation
    /// </summary>
    public List<ChatMessage> Messages { get; set; } = new();
    
    /// <summary>
    /// Maximum number of tokens to generate
    /// </summary>
    public int MaxTokens { get; set; } = 1000;
    
    /// <summary>
    /// Temperature for randomness (0.0 to 2.0)
    /// </summary>
    public double Temperature { get; set; } = 0.7;
    
    /// <summary>
    /// Top-p sampling parameter
    /// </summary>
    public double TopP { get; set; } = 1.0;
    
    /// <summary>
    /// AI provider to use (optional, will use default if not specified)
    /// </summary>
    public string? Provider { get; set; }
    
    /// <summary>
    /// Model to use (optional, will use provider default if not specified)
    /// </summary>
    public string? Model { get; set; }
    
    /// <summary>
    /// Whether to stream the response
    /// </summary>
    public bool Stream { get; set; } = false;
    
    /// <summary>
    /// Conversation ID for tracking (optional)
    /// </summary>
    public string? ConversationId { get; set; }
}
