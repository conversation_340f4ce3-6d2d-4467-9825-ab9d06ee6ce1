using Ai.Integrate.Application.Commands;
using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Ai.Integrate.Application.Handlers;

/// <summary>
/// Handler for ChatCompletionCommand
/// </summary>
public class ChatCompletionCommandHandler : IRequestHandler<ChatCompletionCommand, ChatResponse>
{
    private readonly IEnumerable<IAIService> _aiServices;
    private readonly ILogger<ChatCompletionCommandHandler> _logger;

    public ChatCompletionCommandHandler(
        IEnumerable<IAIService> aiServices,
        ILogger<ChatCompletionCommandHandler> logger)
    {
        _aiServices = aiServices ?? throw new ArgumentNullException(nameof(aiServices));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ChatResponse> Handle(ChatCompletionCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling ChatCompletionCommand with provider: {Provider}", request.Provider ?? "default");

        try
        {
            var aiService = GetAIService(request.Provider);
            
            if (aiService == null)
            {
                _logger.LogError("No AI service found for provider: {Provider}", request.Provider ?? "default");
                return new ChatResponse
                {
                    Success = false,
                    ErrorMessage = $"No AI service found for provider: {request.Provider ?? "default"}"
                };
            }

            // Check if the service is available
            var isAvailable = await aiService.IsAvailableAsync(cancellationToken);
            if (!isAvailable)
            {
                _logger.LogWarning("AI service {Provider} is not available", aiService.ProviderName);
                return new ChatResponse
                {
                    Success = false,
                    ErrorMessage = $"AI service {aiService.ProviderName} is not available"
                };
            }

            // Convert chat messages to a single prompt for now
            // In a full implementation, you'd want to extend IAIService to support chat directly
            var prompt = ConvertChatMessagesToPrompt(request.Messages);
            
            var generateRequest = new GenerateRequest
            {
                Prompt = prompt,
                MaxTokens = request.MaxTokens,
                Temperature = request.Temperature,
                TopP = request.TopP,
                Provider = request.Provider,
                Model = request.Model,
                Stream = request.Stream
            };

            var startTime = DateTime.UtcNow;
            var generateResponse = await aiService.GenerateTextAsync(generateRequest, cancellationToken);
            var endTime = DateTime.UtcNow;
            
            if (!generateResponse.Success)
            {
                return new ChatResponse
                {
                    Success = false,
                    ErrorMessage = generateResponse.ErrorMessage,
                    Provider = generateResponse.Provider
                };
            }

            var response = new ChatResponse
            {
                Message = new ChatMessage
                {
                    Role = "assistant",
                    Content = generateResponse.Text,
                    Timestamp = endTime
                },
                Provider = generateResponse.Provider,
                Model = generateResponse.Model,
                PromptTokens = generateResponse.PromptTokens,
                CompletionTokens = generateResponse.CompletionTokens,
                TotalTokens = generateResponse.TotalTokens,
                Duration = endTime - startTime,
                Timestamp = endTime,
                Success = true,
                ConversationId = request.ConversationId
            };
            
            _logger.LogInformation("Chat completion completed successfully in {Duration}ms", 
                response.Duration.TotalMilliseconds);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during chat completion");
            return new ChatResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred during chat completion: {ex.Message}"
            };
        }
    }

    private IAIService? GetAIService(string? providerName)
    {
        if (string.IsNullOrEmpty(providerName))
        {
            return _aiServices.FirstOrDefault();
        }

        return _aiServices.FirstOrDefault(s => 
            string.Equals(s.ProviderName, providerName, StringComparison.OrdinalIgnoreCase));
    }

    private static string ConvertChatMessagesToPrompt(List<ChatMessage> messages)
    {
        var prompt = string.Join("\n\n", messages.Select(m => $"{m.Role}: {m.Content}"));
        return prompt + "\n\nassistant:";
    }
}
