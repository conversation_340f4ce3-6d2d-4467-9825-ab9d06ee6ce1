using Ai.Integrate.Application.Dtos;
using MediatR;

namespace Ai.Integrate.Application.Queries;

/// <summary>
/// Query to get enhanced health status including provider availability
/// </summary>
public class GetHealthStatusQuery : IRequest<HealthStatusResponse>
{
    /// <summary>
    /// Whether to include detailed provider status
    /// </summary>
    public bool IncludeProviderStatus { get; set; } = true;
    
    /// <summary>
    /// Whether to include system information
    /// </summary>
    public bool IncludeSystemInfo { get; set; } = false;
}
