using Ai.Integrate.Application.Dtos;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace Ai.Integrate.IntegrationTests;

public class ApiEndpointsTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private const string ApiKey = "test-api-key";

    public ApiEndpointsTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.UseEnvironment("Testing");
            builder.ConfigureServices(services =>
            {
                // Override configuration for testing
                services.Configure<Dictionary<string, object>>(config =>
                {
                    config["Authentication:ApiKeys:0"] = ApiKey;
                });
            });
        });

        _client = _factory.CreateClient();
        _client.DefaultRequestHeaders.Add("X-API-Key", ApiKey);
    }

    [Fact]
    public async Task GetProviders_ReturnsSuccessAndCorrectContentType()
    {
        // Act
        var response = await _client.GetAsync("/api/providers");

        // Assert
        response.EnsureSuccessStatusCode();
        Assert.Equal("application/json; charset=utf-8", response.Content.Headers.ContentType?.ToString());

        var content = await response.Content.ReadAsStringAsync();
        var providersResponse = JsonSerializer.Deserialize<ProvidersResponse>(content, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        Assert.NotNull(providersResponse);
        Assert.True(providersResponse.Success);
        Assert.NotEmpty(providersResponse.Providers);
    }

    [Fact]
    public async Task GetProviderModels_ValidProvider_ReturnsModels()
    {
        // Act
        var response = await _client.GetAsync("/api/providers/OpenAI/models");

        // Assert
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync();
        var modelsResponse = JsonSerializer.Deserialize<ModelsResponse>(content, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        Assert.NotNull(modelsResponse);
        Assert.True(modelsResponse.Success);
        Assert.Equal("OpenAI", modelsResponse.Provider);
        Assert.NotEmpty(modelsResponse.Models);
    }

    [Fact]
    public async Task GetProviderModels_InvalidProvider_ReturnsNotFound()
    {
        // Act
        var response = await _client.GetAsync("/api/providers/InvalidProvider/models");

        // Assert
        Assert.Equal(HttpStatusCode.NotFound, response.StatusCode);
    }

    [Fact]
    public async Task PostChatCompletion_ValidRequest_ReturnsSuccess()
    {
        // Arrange
        var request = new ChatRequest
        {
            Messages = new List<ChatMessage>
            {
                new() { Role = "user", Content = "Hello, how are you?" }
            },
            MaxTokens = 100,
            Temperature = 0.7
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/chat", request);

        // Assert
        if (response.StatusCode == HttpStatusCode.BadRequest)
        {
            // This is expected if no real AI provider is configured
            var errorContent = await response.Content.ReadAsStringAsync();
            Assert.Contains("not available", errorContent);
        }
        else
        {
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var chatResponse = JsonSerializer.Deserialize<ChatResponse>(content, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            Assert.NotNull(chatResponse);
        }
    }

    [Fact]
    public async Task PostChatCompletion_InvalidRequest_ReturnsBadRequest()
    {
        // Arrange
        var request = new ChatRequest
        {
            Messages = new List<ChatMessage>(), // Empty messages should fail validation
            MaxTokens = -1, // Invalid max tokens
            Temperature = 3.0 // Invalid temperature
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/chat", request);

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);

        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Validation failed", content);
    }

    [Fact]
    public async Task PostTextCompletion_ValidRequest_ReturnsSuccess()
    {
        // Arrange
        var request = new CompletionRequest
        {
            Prompt = "The future of AI is",
            MaxTokens = 50,
            Temperature = 0.8,
            NumberOfCompletions = 1
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/completion", request);

        // Assert
        if (response.StatusCode == HttpStatusCode.BadRequest)
        {
            // This is expected if no real AI provider is configured
            var errorContent = await response.Content.ReadAsStringAsync();
            Assert.Contains("not available", errorContent);
        }
        else
        {
            response.EnsureSuccessStatusCode();
        }
    }

    [Fact]
    public async Task GetHealthStatus_ReturnsHealthInformation()
    {
        // Act
        var response = await _client.GetAsync("/api/health");

        // Assert
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync();
        var healthResponse = JsonSerializer.Deserialize<HealthStatusResponse>(content, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        Assert.NotNull(healthResponse);
        Assert.NotNull(healthResponse.Status);
        Assert.True(healthResponse.Uptime.TotalSeconds >= 0);
    }

    [Fact]
    public async Task ApiEndpoints_WithoutApiKey_ReturnsUnauthorized()
    {
        // Arrange
        var clientWithoutKey = _factory.CreateClient();

        // Act
        var response = await clientWithoutKey.GetAsync("/api/providers");

        // Assert
        Assert.Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }

    [Fact]
    public async Task HealthCheck_BasicEndpoint_ReturnsHealthy()
    {
        // Act
        var response = await _client.GetAsync("/health");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Equal("Healthy", content);
    }

    [Theory]
    [InlineData("/api/providers")]
    [InlineData("/api/health")]
    public async Task ApiEndpoints_IncludeRateLimitHeaders(string endpoint)
    {
        // Act
        var response = await _client.GetAsync(endpoint);

        // Assert
        response.EnsureSuccessStatusCode();
        Assert.True(response.Headers.Contains("X-RateLimit-Limit-Minute") || 
                   response.Headers.Contains("X-RateLimit-Remaining-Minute"));
    }
}
