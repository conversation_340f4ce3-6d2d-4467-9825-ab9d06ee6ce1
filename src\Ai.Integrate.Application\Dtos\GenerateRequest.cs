using System.ComponentModel.DataAnnotations;

namespace Ai.Integrate.Application.Dtos;

/// <summary>
/// Request model for text generation
/// </summary>
public class GenerateRequest
{
    /// <summary>
    /// The prompt text to generate from
    /// </summary>
    [Required(ErrorMessage = "Prompt is required")]
    [StringLength(10000, ErrorMessage = "Prompt cannot exceed 10000 characters")]
    public string Prompt { get; set; } = string.Empty;
    
    /// <summary>
    /// Maximum number of tokens to generate
    /// </summary>
    [Range(1, 4000, ErrorMessage = "MaxTokens must be between 1 and 4000")]
    public int MaxTokens { get; set; } = 1000;
    
    /// <summary>
    /// Temperature for randomness (0.0 to 2.0)
    /// </summary>
    [Range(0.0, 2.0, ErrorMessage = "Temperature must be between 0.0 and 2.0")]
    public double Temperature { get; set; } = 0.7;
    
    /// <summary>
    /// Top-p sampling parameter
    /// </summary>
    [Range(0.0, 1.0, ErrorMessage = "TopP must be between 0.0 and 1.0")]
    public double TopP { get; set; } = 1.0;
    
    /// <summary>
    /// AI provider to use (optional, will use default if not specified)
    /// </summary>
    public string? Provider { get; set; }
    
    /// <summary>
    /// Model to use (optional, will use provider default if not specified)
    /// </summary>
    public string? Model { get; set; }
    
    /// <summary>
    /// System message for the conversation
    /// </summary>
    public string? SystemMessage { get; set; }
    
    /// <summary>
    /// Whether to stream the response
    /// </summary>
    public bool Stream { get; set; } = false;
}
