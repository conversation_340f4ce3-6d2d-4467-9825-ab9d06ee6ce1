using Ai.Integrate.Application.Dtos;
using FluentValidation;

namespace Ai.Integrate.Application.Validations;

/// <summary>
/// Validator for GenerateRequest
/// </summary>
public class GenerateRequestValidator : AbstractValidator<GenerateRequest>
{
    public GenerateRequestValidator()
    {
        RuleFor(x => x.Prompt)
            .NotEmpty()
            .WithMessage("Prompt is required")
            .MaximumLength(10000)
            .WithMessage("Prompt cannot exceed 10000 characters");

        RuleFor(x => x.MaxTokens)
            .GreaterThan(0)
            .WithMessage("MaxTokens must be greater than 0")
            .LessThanOrEqualTo(4000)
            .WithMessage("MaxTokens cannot exceed 4000");

        RuleFor(x => x.Temperature)
            .GreaterThanOrEqualTo(0.0)
            .WithMessage("Temperature must be greater than or equal to 0.0")
            .LessThanOrEqualTo(2.0)
            .WithMessage("Temperature must be less than or equal to 2.0");

        RuleFor(x => x.TopP)
            .GreaterThanOrEqualTo(0.0)
            .WithMessage("TopP must be greater than or equal to 0.0")
            .LessThanOrEqualTo(1.0)
            .WithMessage("TopP must be less than or equal to 1.0");

        RuleFor(x => x.Provider)
            .Must(BeValidProvider)
            .WithMessage("Provider must be one of: OpenAI, Claude, Google, OpenRouter, Llama, LangChain")
            .When(x => !string.IsNullOrEmpty(x.Provider));

        RuleFor(x => x.SystemMessage)
            .MaximumLength(5000)
            .WithMessage("SystemMessage cannot exceed 5000 characters")
            .When(x => !string.IsNullOrEmpty(x.SystemMessage));
    }

    private static bool BeValidProvider(string? provider)
    {
        if (string.IsNullOrEmpty(provider))
            return true;

        var validProviders = new[] { "OpenAI", "Claude", "Google", "OpenRouter", "Llama", "LangChain" };
        return validProviders.Contains(provider, StringComparer.OrdinalIgnoreCase);
    }
}
