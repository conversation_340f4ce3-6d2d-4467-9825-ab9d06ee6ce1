{"format": 1, "restore": {"D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Infrastructure\\Ai.Integrate.Infrastructure.csproj": {}}, "projects": {"D:\\Code\\Ai.Integration.Service\\src\\AI.Integrate.Domain\\AI.Integrate.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Code\\Ai.Integration.Service\\src\\AI.Integrate.Domain\\AI.Integrate.Domain.csproj", "projectName": "AI.Integrate.Domain", "projectPath": "D:\\Code\\Ai.Integration.Service\\src\\AI.Integrate.Domain\\AI.Integrate.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Code\\Ai.Integration.Service\\src\\AI.Integrate.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Infrastructure\\Ai.Integrate.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Infrastructure\\Ai.Integrate.Infrastructure.csproj", "projectName": "Ai.Integrate.Infrastructure", "projectPath": "D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Infrastructure\\Ai.Integrate.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Code\\Ai.Integration.Service\\src\\AI.Integrate.Domain\\AI.Integrate.Domain.csproj": {"projectPath": "D:\\Code\\Ai.Integration.Service\\src\\AI.Integrate.Domain\\AI.Integrate.Domain.csproj"}, "D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Shared\\Ai.Integrate.Shared.csproj": {"projectPath": "D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Shared\\Ai.Integrate.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[12.0.0, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenAI": {"target": "Package", "version": "[2.1.0, )"}, "Polly": {"target": "Package", "version": "[8.5.2, )"}, "RabbitMQ.Client": {"target": "Package", "version": "[7.1.2, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}, "Scalar.AspNetCore": {"target": "Package", "version": "[2.4.3, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Shared\\Ai.Integrate.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Shared\\Ai.Integrate.Shared.csproj", "projectName": "Ai.Integrate.Shared", "projectPath": "D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Shared\\Ai.Integrate.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Code\\Ai.Integration.Service\\src\\Ai.Integrate.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[12.0.0, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenAI": {"target": "Package", "version": "[2.1.0, )"}, "Polly": {"target": "Package", "version": "[8.5.2, )"}, "RabbitMQ.Client": {"target": "Package", "version": "[7.1.2, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}, "Scalar.AspNetCore": {"target": "Package", "version": "[2.4.3, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}