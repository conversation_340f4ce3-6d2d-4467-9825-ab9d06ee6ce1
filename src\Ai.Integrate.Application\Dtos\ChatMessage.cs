using System.ComponentModel.DataAnnotations;

namespace Ai.Integrate.Application.Dtos;

/// <summary>
/// Represents a message in a chat conversation
/// </summary>
public class ChatMessage
{
    /// <summary>
    /// The role of the message sender (system, user, assistant)
    /// </summary>
    [Required(ErrorMessage = "Role is required")]
    public string Role { get; set; } = string.Empty;
    
    /// <summary>
    /// The content of the message
    /// </summary>
    [Required(ErrorMessage = "Content is required")]
    [StringLength(50000, ErrorMessage = "Content cannot exceed 50000 characters")]
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// Optional name for the message sender
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// Timestamp when the message was created
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Chat completion request model
/// </summary>
public class ChatRequest
{
    /// <summary>
    /// List of messages in the conversation
    /// </summary>
    [Required(ErrorMessage = "Messages are required")]
    [MinLength(1, ErrorMessage = "At least one message is required")]
    public List<ChatMessage> Messages { get; set; } = new();
    
    /// <summary>
    /// Maximum number of tokens to generate
    /// </summary>
    [Range(1, 4000, ErrorMessage = "MaxTokens must be between 1 and 4000")]
    public int MaxTokens { get; set; } = 1000;
    
    /// <summary>
    /// Temperature for randomness (0.0 to 2.0)
    /// </summary>
    [Range(0.0, 2.0, ErrorMessage = "Temperature must be between 0.0 and 2.0")]
    public double Temperature { get; set; } = 0.7;
    
    /// <summary>
    /// Top-p sampling parameter
    /// </summary>
    [Range(0.0, 1.0, ErrorMessage = "TopP must be between 0.0 and 1.0")]
    public double TopP { get; set; } = 1.0;
    
    /// <summary>
    /// AI provider to use (optional, will use default if not specified)
    /// </summary>
    public string? Provider { get; set; }
    
    /// <summary>
    /// Model to use (optional, will use provider default if not specified)
    /// </summary>
    public string? Model { get; set; }
    
    /// <summary>
    /// Whether to stream the response
    /// </summary>
    public bool Stream { get; set; } = false;
    
    /// <summary>
    /// Conversation ID for tracking (optional)
    /// </summary>
    public string? ConversationId { get; set; }
}

/// <summary>
/// Chat completion response model
/// </summary>
public class ChatResponse
{
    /// <summary>
    /// The generated message
    /// </summary>
    public ChatMessage Message { get; set; } = new();
    
    /// <summary>
    /// The provider that generated the response
    /// </summary>
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// The model used for generation
    /// </summary>
    public string Model { get; set; } = string.Empty;
    
    /// <summary>
    /// Number of tokens in the prompt
    /// </summary>
    public int PromptTokens { get; set; }
    
    /// <summary>
    /// Number of tokens in the completion
    /// </summary>
    public int CompletionTokens { get; set; }
    
    /// <summary>
    /// Total number of tokens used
    /// </summary>
    public int TotalTokens { get; set; }
    
    /// <summary>
    /// Time taken to generate the response
    /// </summary>
    public TimeSpan Duration { get; set; }
    
    /// <summary>
    /// Timestamp when the response was generated
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Whether the response was successful
    /// </summary>
    public bool Success { get; set; } = true;
    
    /// <summary>
    /// Error message if the response was not successful
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Conversation ID for tracking
    /// </summary>
    public string? ConversationId { get; set; }
    
    /// <summary>
    /// Additional metadata from the provider
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}
