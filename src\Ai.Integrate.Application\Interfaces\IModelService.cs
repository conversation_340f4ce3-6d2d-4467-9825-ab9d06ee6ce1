using Ai.Integrate.Application.Dtos;

namespace Ai.Integrate.Application.Interfaces;

/// <summary>
/// Interface for model management services
/// </summary>
public interface IModelService
{
    /// <summary>
    /// Get available models for the provider
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of available models</returns>
    Task<List<ModelInfo>> GetModelsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get specific model information
    /// </summary>
    /// <param name="modelId">Model identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Model information</returns>
    Task<ModelInfo?> GetModelAsync(string modelId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Check if a model is available
    /// </summary>
    /// <param name="modelId">Model identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if model is available</returns>
    Task<bool> IsModelAvailableAsync(string modelId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get the default model for the provider
    /// </summary>
    /// <returns>Default model identifier</returns>
    string GetDefaultModel();
}
