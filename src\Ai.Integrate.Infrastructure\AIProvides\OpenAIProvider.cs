﻿using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using Ai.Integrate.Infrastructure.Config;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;

namespace Ai.Integrate.Infrastructure.AIProvides;

/// <summary>
/// OpenAI provider implementation
/// </summary>
public class OpenAIProvider : IAIService
{
    private readonly HttpClient _httpClient;
    private readonly AISettings _settings;
    private readonly ILogger<OpenAIProvider> _logger;

    public string ProviderName => "OpenAI";

    public OpenAIProvider(
        IHttpClientFactory httpClientFactory,
        IOptions<AISettings> settings,
        ILogger<OpenAIProvider> logger)
    {
        _httpClient = httpClientFactory.CreateClient("OpenAI");
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task<GenerateResponse> GenerateTextAsync(GenerateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var openAIRequest = new
            {
                model = request.Model ?? _settings.OpenAI.DefaultModel,
                messages = new[]
                {
                    new { role = "system", content = request.SystemMessage ?? "You are a helpful assistant." },
                    new { role = "user", content = request.Prompt }
                },
                max_tokens = request.MaxTokens,
                temperature = request.Temperature,
                top_p = request.TopP,
                stream = request.Stream
            };

            var json = JsonSerializer.Serialize(openAIRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/chat/completions", content, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var openAIResponse = JsonSerializer.Deserialize<OpenAIResponse>(responseContent);

            return new GenerateResponse
            {
                Text = openAIResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? string.Empty,
                Provider = ProviderName,
                Model = openAIRequest.model,
                PromptTokens = openAIResponse?.Usage?.PromptTokens ?? 0,
                CompletionTokens = openAIResponse?.Usage?.CompletionTokens ?? 0,
                TotalTokens = openAIResponse?.Usage?.TotalTokens ?? 0,
                Success = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating text with OpenAI");
            return new GenerateResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                Provider = ProviderName
            };
        }
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        if (!_settings.OpenAI.Enabled || string.IsNullOrEmpty(_settings.OpenAI.ApiKey))
        {
            return false;
        }

        try
        {
            var response = await _httpClient.GetAsync("/models", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }
}

// OpenAI API response models
public class OpenAIResponse
{
    public Choice[]? Choices { get; set; }
    public Usage? Usage { get; set; }
}

public class Choice
{
    public Message? Message { get; set; }
}

public class Message
{
    public string? Content { get; set; }
}

public class Usage
{
    public int PromptTokens { get; set; }
    public int CompletionTokens { get; set; }
    public int TotalTokens { get; set; }
}