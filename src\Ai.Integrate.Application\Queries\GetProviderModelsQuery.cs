using Ai.Integrate.Application.Dtos;
using MediatR;

namespace Ai.Integrate.Application.Queries;

/// <summary>
/// Query to get available models for a specific provider
/// </summary>
public class GetProviderModelsQuery : IRequest<ModelsResponse>
{
    /// <summary>
    /// The provider name (OpenAI, Claude, Google, etc.)
    /// </summary>
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether to include only enabled models
    /// </summary>
    public bool EnabledOnly { get; set; } = true;
    
    /// <summary>
    /// Whether to include model capabilities information
    /// </summary>
    public bool IncludeCapabilities { get; set; } = false;
}
