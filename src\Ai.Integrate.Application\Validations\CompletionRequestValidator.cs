using Ai.Integrate.Application.Dtos;
using FluentValidation;

namespace Ai.Integrate.Application.Validations;

/// <summary>
/// Validator for CompletionRequest
/// </summary>
public class CompletionRequestValidator : AbstractValidator<CompletionRequest>
{
    public CompletionRequestValidator()
    {
        RuleFor(x => x.Prompt)
            .NotEmpty()
            .WithMessage("Prompt is required")
            .MaximumLength(10000)
            .WithMessage("Prompt cannot exceed 10000 characters");

        RuleFor(x => x.MaxTokens)
            .GreaterThan(0)
            .WithMessage("MaxTokens must be greater than 0")
            .LessThanOrEqualTo(4000)
            .WithMessage("MaxTokens cannot exceed 4000");

        RuleFor(x => x.Temperature)
            .GreaterThanOrEqualTo(0.0)
            .WithMessage("Temperature must be greater than or equal to 0.0")
            .LessThanOrEqualTo(2.0)
            .WithMessage("Temperature must be less than or equal to 2.0");

        RuleFor(x => x.TopP)
            .GreaterThanOrEqualTo(0.0)
            .WithMessage("TopP must be greater than or equal to 0.0")
            .LessThanOrEqualTo(1.0)
            .WithMessage("TopP must be less than or equal to 1.0");

        RuleFor(x => x.Provider)
            .Must(BeValidProvider)
            .WithMessage("Provider must be one of: OpenAI, Claude, Google, OpenRouter, Llama, LangChain")
            .When(x => !string.IsNullOrEmpty(x.Provider));

        RuleFor(x => x.NumberOfCompletions)
            .GreaterThan(0)
            .WithMessage("NumberOfCompletions must be greater than 0")
            .LessThanOrEqualTo(10)
            .WithMessage("NumberOfCompletions cannot exceed 10");

        RuleFor(x => x.StopSequences)
            .Must(HaveValidStopSequences)
            .WithMessage("Stop sequences cannot be empty and cannot exceed 10 items")
            .When(x => x.StopSequences != null);
    }

    private static bool BeValidProvider(string? provider)
    {
        if (string.IsNullOrEmpty(provider))
            return true;

        var validProviders = new[] { "OpenAI", "Claude", "Google", "OpenRouter", "Llama", "LangChain" };
        return validProviders.Contains(provider, StringComparer.OrdinalIgnoreCase);
    }

    private static bool HaveValidStopSequences(List<string>? stopSequences)
    {
        if (stopSequences == null)
            return true;

        return stopSequences.Count <= 10 && stopSequences.All(s => !string.IsNullOrWhiteSpace(s));
    }
}
