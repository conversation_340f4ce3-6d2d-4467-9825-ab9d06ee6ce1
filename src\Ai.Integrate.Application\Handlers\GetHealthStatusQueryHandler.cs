using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using Ai.Integrate.Application.Queries;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.InteropServices;

namespace Ai.Integrate.Application.Handlers;

/// <summary>
/// Handler for GetHealthStatusQuery
/// </summary>
public class GetHealthStatusQueryHandler : IRequestHandler<GetHealthStatusQuery, HealthStatusResponse>
{
    private readonly IEnumerable<IAIService> _aiServices;
    private readonly ILogger<GetHealthStatusQueryHandler> _logger;
    private static readonly DateTime _startTime = DateTime.UtcNow;

    public GetHealthStatusQueryHandler(
        IEnumerable<IAIService> aiServices,
        ILogger<GetHealthStatusQueryHandler> logger)
    {
        _aiServices = aiServices ?? throw new ArgumentNullException(nameof(aiServices));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<HealthStatusResponse> Handle(GetHealthStatusQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling GetHealthStatusQuery");

        try
        {
            var response = new HealthStatusResponse
            {
                Status = "Healthy",
                Version = GetApplicationVersion(),
                Uptime = DateTime.UtcNow - _startTime,
                IsHealthy = true,
                Warnings = new List<string>()
            };

            // Check provider status if requested
            if (request.IncludeProviderStatus)
            {
                response.Providers = await GetProviderHealthStatus(cancellationToken);
                
                // Update overall health based on provider status
                var unhealthyProviders = response.Providers.Where(p => p.Status == "Unhealthy").ToList();
                var degradedProviders = response.Providers.Where(p => p.Status == "Degraded").ToList();
                
                if (unhealthyProviders.Any())
                {
                    response.Status = "Degraded";
                    response.Warnings.Add($"{unhealthyProviders.Count} provider(s) are unhealthy");
                }
                else if (degradedProviders.Any())
                {
                    response.Status = "Degraded";
                    response.Warnings.Add($"{degradedProviders.Count} provider(s) are degraded");
                }
            }

            // Include system information if requested
            if (request.IncludeSystemInfo)
            {
                response.SystemInfo = GetSystemInfo();
            }

            // Add additional health check details
            response.Details = new Dictionary<string, object>
            {
                { "timestamp", DateTime.UtcNow },
                { "environment", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown" },
                { "machineName", Environment.MachineName },
                { "processId", Environment.ProcessId },
                { "workingSet", GC.GetTotalMemory(false) }
            };

            _logger.LogInformation("Health check completed with status: {Status}", response.Status);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during health check");
            return new HealthStatusResponse
            {
                Status = "Unhealthy",
                IsHealthy = false,
                Version = GetApplicationVersion(),
                Uptime = DateTime.UtcNow - _startTime,
                Warnings = new List<string> { $"Health check failed: {ex.Message}" }
            };
        }
    }

    private async Task<List<ProviderHealthStatus>> GetProviderHealthStatus(CancellationToken cancellationToken)
    {
        var providerStatuses = new List<ProviderHealthStatus>();

        foreach (var aiService in _aiServices)
        {
            var stopwatch = Stopwatch.StartNew();
            var status = new ProviderHealthStatus
            {
                Name = aiService.ProviderName,
                LastSuccessfulCheck = DateTime.UtcNow
            };

            try
            {
                var isAvailable = await aiService.IsAvailableAsync(cancellationToken);
                stopwatch.Stop();
                
                status.Available = isAvailable;
                status.ResponseTime = stopwatch.Elapsed;
                status.Status = isAvailable ? "Healthy" : "Unhealthy";
                
                if (isAvailable)
                {
                    status.LastSuccessfulCheck = DateTime.UtcNow;
                }
                else
                {
                    status.ErrorMessage = "Provider is not available";
                }

                // Add response time warning if slow
                if (stopwatch.ElapsedMilliseconds > 5000)
                {
                    status.Status = "Degraded";
                    status.Details = new Dictionary<string, object>
                    {
                        { "warning", "Slow response time" },
                        { "responseTimeMs", stopwatch.ElapsedMilliseconds }
                    };
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                status.Available = false;
                status.Status = "Unhealthy";
                status.ErrorMessage = ex.Message;
                status.ResponseTime = stopwatch.Elapsed;
                
                _logger.LogWarning(ex, "Health check failed for provider {Provider}", aiService.ProviderName);
            }

            providerStatuses.Add(status);
        }

        return providerStatuses;
    }

    private static SystemInfo GetSystemInfo()
    {
        var process = Process.GetCurrentProcess();
        
        return new SystemInfo
        {
            OperatingSystem = RuntimeInformation.OSDescription,
            RuntimeVersion = RuntimeInformation.FrameworkDescription,
            AvailableMemoryMB = GC.GetTotalMemory(false) / 1024 / 1024,
            UsedMemoryMB = process.WorkingSet64 / 1024 / 1024,
            CpuUsagePercent = GetCpuUsage(),
            ThreadCount = process.Threads.Count,
            StartTime = _startTime
        };
    }

    private static double GetCpuUsage()
    {
        // This is a simplified CPU usage calculation
        // In a production environment, you might want to use a more sophisticated approach
        try
        {
            var process = Process.GetCurrentProcess();
            return process.TotalProcessorTime.TotalMilliseconds / Environment.TickCount * 100;
        }
        catch
        {
            return 0.0;
        }
    }

    private static string GetApplicationVersion()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version;
            return version?.ToString() ?? "Unknown";
        }
        catch
        {
            return "Unknown";
        }
    }
}
