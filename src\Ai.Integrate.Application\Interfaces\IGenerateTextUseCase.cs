using Ai.Integrate.Application.Dtos;

namespace Ai.Integrate.Application.Interfaces;

/// <summary>
/// Interface for the generate text use case
/// </summary>
public interface IGenerateTextUseCase
{
    /// <summary>
    /// Execute the generate text use case
    /// </summary>
    /// <param name="request">The generation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The generation response</returns>
    Task<GenerateResponse> ExecuteAsync(GenerateRequest request, CancellationToken cancellationToken = default);
}
