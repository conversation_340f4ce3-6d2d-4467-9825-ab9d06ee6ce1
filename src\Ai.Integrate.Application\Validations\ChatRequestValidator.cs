using Ai.Integrate.Application.Dtos;
using FluentValidation;

namespace Ai.Integrate.Application.Validations;

/// <summary>
/// Validator for ChatRequest
/// </summary>
public class ChatRequestValidator : AbstractValidator<ChatRequest>
{
    public ChatRequestValidator()
    {
        RuleFor(x => x.Messages)
            .NotEmpty()
            .WithMessage("Messages are required")
            .Must(HaveAtLeastOneUserMessage)
            .WithMessage("At least one user message is required");

        RuleForEach(x => x.Messages)
            .SetValidator(new ChatMessageValidator());

        RuleFor(x => x.MaxTokens)
            .GreaterThan(0)
            .WithMessage("MaxTokens must be greater than 0")
            .LessThanOrEqualTo(4000)
            .WithMessage("MaxTokens cannot exceed 4000");

        RuleFor(x => x.Temperature)
            .GreaterThanOrEqualTo(0.0)
            .WithMessage("Temperature must be greater than or equal to 0.0")
            .LessThanOrEqualTo(2.0)
            .WithMessage("Temperature must be less than or equal to 2.0");

        RuleFor(x => x.TopP)
            .GreaterThanOrEqualTo(0.0)
            .WithMessage("TopP must be greater than or equal to 0.0")
            .LessThanOrEqualTo(1.0)
            .WithMessage("TopP must be less than or equal to 1.0");

        RuleFor(x => x.Provider)
            .Must(BeValidProvider)
            .WithMessage("Provider must be one of: OpenAI, Claude, Google, OpenRouter, Llama, LangChain")
            .When(x => !string.IsNullOrEmpty(x.Provider));

        RuleFor(x => x.ConversationId)
            .MaximumLength(100)
            .WithMessage("ConversationId cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.ConversationId));
    }

    private static bool HaveAtLeastOneUserMessage(List<ChatMessage> messages)
    {
        return messages.Any(m => string.Equals(m.Role, "user", StringComparison.OrdinalIgnoreCase));
    }

    private static bool BeValidProvider(string? provider)
    {
        if (string.IsNullOrEmpty(provider))
            return true;

        var validProviders = new[] { "OpenAI", "Claude", "Google", "OpenRouter", "Llama", "LangChain" };
        return validProviders.Contains(provider, StringComparer.OrdinalIgnoreCase);
    }
}

/// <summary>
/// Validator for ChatMessage
/// </summary>
public class ChatMessageValidator : AbstractValidator<ChatMessage>
{
    public ChatMessageValidator()
    {
        RuleFor(x => x.Role)
            .NotEmpty()
            .WithMessage("Role is required")
            .Must(BeValidRole)
            .WithMessage("Role must be one of: system, user, assistant");

        RuleFor(x => x.Content)
            .NotEmpty()
            .WithMessage("Content is required")
            .MaximumLength(50000)
            .WithMessage("Content cannot exceed 50000 characters");

        RuleFor(x => x.Name)
            .MaximumLength(100)
            .WithMessage("Name cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.Name));
    }

    private static bool BeValidRole(string role)
    {
        var validRoles = new[] { "system", "user", "assistant" };
        return validRoles.Contains(role, StringComparer.OrdinalIgnoreCase);
    }
}
