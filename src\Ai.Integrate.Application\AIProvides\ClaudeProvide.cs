﻿using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using Ai.Integrate.Infrastructure.Config;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Ai.Integrate.Application.AIProvides;

/// <summary>
/// Claude (Anthropic) provider implementation
/// </summary>
public class ClaudeProvider(
    IHttpClientFactory httpClientFactory,
    IOptions<AISettings> settings,
    ILogger<ClaudeProvider> logger)
    : IAIService, IChatService, IModelService
{
    private readonly HttpClient _httpClient = httpClientFactory.CreateClient("Claude");
    private readonly AISettings _settings = settings.Value;

    public string ProviderName => "Claude";

    public async Task<GenerateResponse> GenerateTextAsync(GenerateRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var messages = new List<ClaudeMessage>();

            if (!string.IsNullOrEmpty(request.SystemMessage))
            {
                messages.Add(new ClaudeMessage
                {
                    Role = "system",
                    Content = request.SystemMessage
                });
            }

            messages.Add(new ClaudeMessage
            {
                Role = "user",
                Content = request.Prompt
            });

            var claudeRequest = new
            {
                model = request.Model ?? _settings.Claude.DefaultModel,
                max_tokens = request.MaxTokens,
                temperature = request.Temperature,
                top_p = request.TopP,
                messages = messages,
                stream = request.Stream,
                system = request.SystemMessage
            };

            var json = JsonSerializer.Serialize(claudeRequest, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/v1/messages", content, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var claudeResponse = JsonSerializer.Deserialize<ClaudeResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return new GenerateResponse
            {
                Text = claudeResponse?.Content?.FirstOrDefault()?.Text ?? string.Empty,
                Provider = ProviderName,
                Model = claudeRequest.model,
                PromptTokens = claudeResponse?.Usage?.InputTokens ?? 0,
                CompletionTokens = claudeResponse?.Usage?.OutputTokens ?? 0,
                TotalTokens = (claudeResponse?.Usage?.InputTokens ?? 0) + (claudeResponse?.Usage?.OutputTokens ?? 0),
                Success = true
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating text with Claude");
            return new GenerateResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                Provider = ProviderName
            };
        }
    }

    public async Task<ChatResponse> ChatCompletionAsync(List<ChatMessage> messages, ChatOptions options,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var claudeMessages = messages.Select(m => new ClaudeMessage
            {
                Role = m.Role,
                Content = m.Content
            }).ToList();

            var claudeRequest = new
            {
                model = options.Model ?? _settings.Claude.DefaultModel,
                max_tokens = options.MaxTokens,
                temperature = options.Temperature,
                top_p = options.TopP,
                messages = claudeMessages,
                stream = options.Stream,
                stop_sequences = options.StopSequences
            };

            var json = JsonSerializer.Serialize(claudeRequest, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/v1/messages", content, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var claudeResponse = JsonSerializer.Deserialize<ClaudeResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return new ChatResponse
            {
                Message = new ChatMessage
                {
                    Role = "assistant",
                    Content = claudeResponse?.Content?.FirstOrDefault()?.Text ?? string.Empty,
                    Timestamp = DateTime.UtcNow
                },
                Provider = ProviderName,
                Model = claudeRequest.model,
                PromptTokens = claudeResponse?.Usage?.InputTokens ?? 0,
                CompletionTokens = claudeResponse?.Usage?.OutputTokens ?? 0,
                TotalTokens = (claudeResponse?.Usage?.InputTokens ?? 0) + (claudeResponse?.Usage?.OutputTokens ?? 0),
                Success = true,
                ConversationId = options.ConversationId
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error with Claude chat completion");
            return new ChatResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                Provider = ProviderName
            };
        }
    }

    public async IAsyncEnumerable<ChatResponse> ChatCompletionStreamAsync(List<ChatMessage> messages,
        ChatOptions options, [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        // Implementation for streaming would go here
        // For now, return a single response
        var response = await ChatCompletionAsync(messages, options, cancellationToken);
        yield return response;
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        if (!_settings.Claude.Enabled || string.IsNullOrEmpty(_settings.Claude.ApiKey))
        {
            return false;
        }

        try
        {
            var testRequest = new
            {
                model = _settings.Claude.DefaultModel,
                max_tokens = 1,
                messages = new[] { new { role = "user", content = "Hi" } }
            };

            var json = JsonSerializer.Serialize(testRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/v1/messages", content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    public async Task<List<ModelInfo>> GetModelsAsync(CancellationToken cancellationToken = default)
    {
        // Claude doesn't have a models endpoint, so we return the known models
        return
        [
            new()
            {
                Id = "claude-3-opus-20240229", Name = "Claude 3 Opus", Description = "Most powerful Claude model",
                MaxTokens = 200000, Type = "chat", Enabled = true, IsDefault = false
            },

            new()
            {
                Id = "claude-3-sonnet-20240229", Name = "Claude 3 Sonnet",
                Description = "Balanced performance and speed", MaxTokens = 200000, Type = "chat", Enabled = true,
                IsDefault = true
            },

            new()
            {
                Id = "claude-3-haiku-20240307", Name = "Claude 3 Haiku", Description = "Fastest Claude model",
                MaxTokens = 200000, Type = "chat", Enabled = true, IsDefault = false
            }
        ];
    }

    public async Task<ModelInfo?> GetModelAsync(string modelId, CancellationToken cancellationToken = default)
    {
        var models = await GetModelsAsync(cancellationToken);
        return models.FirstOrDefault(m => m.Id == modelId);
    }

    public async Task<bool> IsModelAvailableAsync(string modelId, CancellationToken cancellationToken = default)
    {
        var model = await GetModelAsync(modelId, cancellationToken);
        return model?.Enabled == true;
    }

    public string GetDefaultModel()
    {
        return _settings.Claude.DefaultModel;
    }
}

public class ClaudeResponse
{
    public ClaudeContent[]? Content { get; set; }
    public ClaudeUsage? Usage { get; set; }
    public string? StopReason { get; set; }
}

public class ClaudeContent
{
    public string? Type { get; set; }
    public string? Text { get; set; }
}

public class ClaudeMessage
{
    public string Role { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
}

public class ClaudeUsage
{
    public int InputTokens { get; set; }
    public int OutputTokens { get; set; }
}