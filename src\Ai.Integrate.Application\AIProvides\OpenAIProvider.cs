using System.Text;
using System.Text.Json;
using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using Ai.Integrate.Infrastructure.Config;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Ai.Integrate.Application.AIProvides;

/// <summary>
/// OpenAI provider implementation
/// </summary>
public class OpenAiProvider(
    IHttpClientFactory httpClientFactory,
    IOptions<AISettings> settings,
    ILogger<OpenAiProvider> logger)
    : IAIService, IChatService, IModelService
{
    private readonly HttpClient _httpClient = httpClientFactory.CreateClient("OpenAI");
    private readonly AISettings _settings = settings.Value;

    public string ProviderName => "OpenAI";

    public async Task<GenerateResponse> GenerateTextAsync(GenerateRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var openAiRequest = new
            {
                model = request.Model ?? _settings.OpenAI.DefaultModel,
                messages = new[]
                {
                    new { role = "system", content = request.SystemMessage ?? "You are a helpful assistant." },
                    new { role = "user", content = request.Prompt }
                },
                max_tokens = request.MaxTokens,
                temperature = request.Temperature,
                top_p = request.TopP,
                stream = request.Stream
            };

            var json = JsonSerializer.Serialize(openAiRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/chat/completions", content, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var openAiResponse = JsonSerializer.Deserialize<OpenAIResponse>(responseContent);

            return new GenerateResponse
            {
                Text = openAiResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? string.Empty,
                Provider = ProviderName,
                Model = openAiRequest.model,
                PromptTokens = openAiResponse?.Usage?.PromptTokens ?? 0,
                CompletionTokens = openAiResponse?.Usage?.CompletionTokens ?? 0,
                TotalTokens = openAiResponse?.Usage?.TotalTokens ?? 0,
                Success = true
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating text with OpenAI");
            return new GenerateResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                Provider = ProviderName
            };
        }
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        if (!_settings.OpenAI.Enabled || string.IsNullOrEmpty(_settings.OpenAI.ApiKey))
        {
            return false;
        }

        try
        {
            var response = await _httpClient.GetAsync("/models", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }
    public async Task<ChatResponse> ChatCompletionAsync(List<ChatMessage> messages, ChatOptions options,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var openAiMessages = messages.Select(m => new
            {
                role = m.Role.ToLowerInvariant(),
                content = m.Content
            }).ToArray();

            var openAiRequest = new
            {
                model = options.Model ?? _settings.OpenAI.DefaultModel,
                messages = openAiMessages,
                max_tokens = options.MaxTokens,
                temperature = options.Temperature,
                top_p = options.TopP,
                stream = false
            };

            var json = JsonSerializer.Serialize(openAiRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/chat/completions", content, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var openAiResponse = JsonSerializer.Deserialize<OpenAIResponse>(responseContent);

            var messageContent = openAiResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? string.Empty;

            return new ChatResponse
            {
                Message = new ChatMessage
                {
                    Role = "assistant",
                    Content = messageContent,
                    Timestamp = DateTime.UtcNow
                },
                Provider = ProviderName,
                Model = options.Model ?? _settings.OpenAI.DefaultModel,
                PromptTokens = openAiResponse?.Usage?.PromptTokens ?? 0,
                CompletionTokens = openAiResponse?.Usage?.CompletionTokens ?? 0,
                TotalTokens = openAiResponse?.Usage?.TotalTokens ?? 0,
                Success = true
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating chat completion with OpenAI");
            return new ChatResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                Provider = ProviderName
            };
        }
    }

    public async IAsyncEnumerable<ChatResponse> ChatCompletionStreamAsync(List<ChatMessage> messages, ChatOptions options,
        [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        // This implementation would require SSE (Server-Sent Events) handling
        // Placeholder for now - in a real implementation we'd stream the responses
        var response = await ChatCompletionAsync(messages, options, cancellationToken);
        yield return response;
    }
    public async Task<List<ModelInfo>> GetModelsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync("/models", cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var openAiResponse = JsonSerializer.Deserialize<OpenAIModelsResponse>(responseContent);

            if (openAiResponse?.Data == null)
                return new List<ModelInfo>();

            var defaultModelId = _settings.OpenAI.DefaultModel;

            logger.LogInformation("Retrieved {Count} models from OpenAI API", openAiResponse.Data.Count);

            return openAiResponse.Data
                .Where(m => !m.Id.StartsWith("ft:")) // Filter out fine-tuned models
                .Select(d => new ModelInfo
                {
                    Id = d.Id,
                    Name = GetDisplayName(d.Id),
                    Description = $"OpenAI {d.Id} model" + (d.OwnedBy != "openai" ? $" (owned by {d.OwnedBy})" : ""),
                    Enabled = IsModelSupported(d.Id),
                    // Context window might be called MaxTokens in our domain model
                    MaxTokens = d.MaxTokens,
                    Type = DetermineModelType(d.Id),
                    Version = DateTimeOffset.FromUnixTimeSeconds(d.Created).ToString("yyyy-MM-dd"),
                    IsDefault = d.Id == defaultModelId,
                    // Add capabilities based on model type
                    Capabilities = GetModelCapabilities(d.Id)
                })
                .ToList();
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error getting models from OpenAI");
            return new List<ModelInfo>();
        }
    }

    private string GetDisplayName(string modelId)
    {
        return modelId.Replace("-", " ").Replace(":", " ")
            .Split(' ')
            .Select(s => char.ToUpper(s[0]) + s[1..])
            .Aggregate((a, b) => $"{a} {b}");
    }
    private string DetermineModelType(string modelId)
    {
        if (modelId.Contains("gpt-4") || modelId.Contains("gpt-3.5"))
            return "chat";
        if (modelId.Contains("dall-e"))
            return "image";
        if (modelId.Contains("embedding"))
            return "embedding";
        if (modelId.Contains("tts"))
            return "speech";
        return "completion";
    }
    private bool IsModelSupported(string modelId)
    {
        // Common GPT models that are typically supported
        var commonSupportedModels = new[]
        {
            "gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
        };

        return commonSupportedModels.Any(m => modelId.StartsWith(m));
    }

    private List<string> GetModelCapabilities(string modelId)
    {
        var capabilities = new List<string>();

        // Base capabilities
        capabilities.Add("text-generation");

        // Model-specific capabilities
        if (modelId.Contains("gpt-4") || modelId.Contains("gpt-3.5"))
        {
            capabilities.AddRange(new[] { "chat", "completion", "streaming" });

            // Advanced capabilities for newer models
            if (modelId.Contains("gpt-4") || modelId.Contains("turbo"))
            {
                capabilities.Add("function-calling");
            }

            // Vision capabilities
            if (modelId.Contains("vision"))
            {
                capabilities.AddRange(new[] { "vision", "multimodal" });
            }
        }
        else if (modelId.Contains("dall-e"))
        {
            capabilities.AddRange(new[] { "image-generation", "creativity" });
        }
        else if (modelId.Contains("whisper"))
        {
            capabilities.AddRange(new[] { "speech-to-text", "transcription", "translation" });
        }
        else if (modelId.Contains("tts"))
        {
            capabilities.Add("text-to-speech");
        }

        return capabilities;
    }
    public async Task<ModelInfo?> GetModelAsync(string modelId, CancellationToken cancellationToken = default)
    {
        var models = await GetModelsAsync(cancellationToken);
        return models.FirstOrDefault(m => m.Id == modelId);
    }

    public async Task<bool> IsModelAvailableAsync(string modelId, CancellationToken cancellationToken = default)
    {
        var model = await GetModelAsync(modelId, cancellationToken);
        return model?.Enabled == true;
    }

    public string GetDefaultModel()
    {
        return _settings.OpenAI.DefaultModel;
    }
}

// OpenAI API response models
public class OpenAIResponse
{
    public Choice[]? Choices { get; set; }
    public Usage? Usage { get; set; }
}

public class Choice
{
    public Message? Message { get; set; }
}

public class Message
{
    public string? Content { get; set; }
}

public class Usage
{
    public int PromptTokens { get; set; }
    public int CompletionTokens { get; set; }
    public int TotalTokens { get; set; }
}