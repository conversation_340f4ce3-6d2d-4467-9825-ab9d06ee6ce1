using Ai.Integrate.Application.Dtos;

namespace Ai.Integrate.Application.Interfaces;

/// <summary>
/// Interface for AI service providers
/// </summary>
public interface IAIService
{
    /// <summary>
    /// Generate text using the AI provider
    /// </summary>
    /// <param name="request">The generation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The generation response</returns>
    Task<GenerateResponse> GenerateTextAsync(GenerateRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get the provider name
    /// </summary>
    string ProviderName { get; }
    
    /// <summary>
    /// Check if the provider is available
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if available, false otherwise</returns>
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);
}
