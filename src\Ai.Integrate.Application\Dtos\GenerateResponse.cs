namespace Ai.Integrate.Application.Dtos;

/// <summary>
/// Response model for text generation
/// </summary>
public class GenerateResponse
{
    /// <summary>
    /// The generated text
    /// </summary>
    public string Text { get; set; } = string.Empty;
    
    /// <summary>
    /// The provider that generated the response
    /// </summary>
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// The model used for generation
    /// </summary>
    public string Model { get; set; } = string.Empty;
    
    /// <summary>
    /// Number of tokens in the prompt
    /// </summary>
    public int PromptTokens { get; set; }
    
    /// <summary>
    /// Number of tokens in the completion
    /// </summary>
    public int CompletionTokens { get; set; }
    
    /// <summary>
    /// Total number of tokens used
    /// </summary>
    public int TotalTokens { get; set; }
    
    /// <summary>
    /// Time taken to generate the response
    /// </summary>
    public TimeSpan Duration { get; set; }
    
    /// <summary>
    /// Timestamp when the response was generated
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Whether the response was successful
    /// </summary>
    public bool Success { get; set; } = true;
    
    /// <summary>
    /// Error message if the response was not successful
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Additional metadata from the provider
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}
