using Ai.Integrate.Application.Commands;
using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using Ai.Integrate.Application.Queries;
using Ai.Integrate.Infrastructure.AIProvides;
using Ai.Integrate.Infrastructure.Config;
using Ai.Integrate.Shared.Extensions;
using Ai.Integrate.Shared.Middleware;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Scalar.AspNetCore;
using Serilog;
using System.ComponentModel.DataAnnotations;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/ai-integration-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddOpenApi();

// Add configuration settings
builder.Services.AddConfigurationSettings(builder.Configuration);

// Add application services
builder.Services.AddApplicationServices();

// Add HTTP clients with Polly policies
builder.Services.AddHttpClientsWithPolly(builder.Configuration);

// Add RestSharp clients
builder.Services.AddRestSharpClients(builder.Configuration);

// Register AI providers
builder.Services.AddScoped<IAIService, OpenAIProvider>();
// Add other providers when implemented:
// builder.Services.AddScoped<IAIService, ClaudeProvider>();
// builder.Services.AddScoped<IAIService, GoogleProvider>();

// Add FluentValidation
builder.Services.AddValidatorsFromAssemblyContaining<GenerateRequest>();

// Add health checks
builder.Services.AddHealthChecks();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
app.UseMiddleware<GlobalExceptionMiddleware>();

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference(options =>
    {
        options.Title = "AI Integration Service API";
        options.Theme = ScalarTheme.BluePlanet;
        options.ShowSidebar = true;
        options.Authentication = new ScalarAuthenticationOptions
        {
            PreferredSecurityScheme = "ApiKey",
            ApiKey = new ApiKeyOptions
            {
                Token = "your-api-key-here"
            }
        };
    });
}

app.UseCors();
app.UseHttpsRedirection();

// Add API key authentication middleware
app.UseMiddleware<ApiKeyAuthMiddleware>();

// Add health check endpoint
app.MapHealthChecks("/health");

// Enhanced health check endpoint
app.MapGet("/api/health", GetHealthStatusEndpoint)
    .WithName("GetHealthStatus")
    .WithSummary("Get enhanced health status")
    .WithDescription("Get detailed health status including provider availability")
    .Produces<HealthStatusResponse>(200)
    .Produces<ErrorResponse>(500);

// Get providers endpoint
app.MapGet("/api/providers", GetProvidersEndpoint)
    .WithName("GetProviders")
    .WithSummary("Get available AI providers")
    .WithDescription("Get list of available AI providers with their status and capabilities")
    .Produces<ProvidersResponse>(200)
    .Produces<ErrorResponse>(401)
    .Produces<ErrorResponse>(500);

// Get provider models endpoint
app.MapGet("/api/providers/{provider}/models", GetProviderModelsEndpoint)
    .WithName("GetProviderModels")
    .WithSummary("Get available models for a provider")
    .WithDescription("Get available models for a specific AI provider")
    .Produces<ModelsResponse>(200)
    .Produces<ErrorResponse>(400)
    .Produces<ErrorResponse>(401)
    .Produces<ErrorResponse>(404)
    .Produces<ErrorResponse>(500);

// Chat completion endpoint
app.MapPost("/api/chat", ChatCompletionEndpoint)
    .WithName("ChatCompletion")
    .WithSummary("Chat completion with conversation history")
    .WithDescription("Generate chat responses with conversation history support")
    .Produces<ChatResponse>(200)
    .Produces<ErrorResponse>(400)
    .Produces<ErrorResponse>(401)
    .Produces<ErrorResponse>(500);

// Text completion endpoint
app.MapPost("/api/completion", TextCompletionEndpoint)
    .WithName("TextCompletion")
    .WithSummary("Text completion")
    .WithDescription("Generate text completions")
    .Produces<CompletionResponse>(200)
    .Produces<ErrorResponse>(400)
    .Produces<ErrorResponse>(401)
    .Produces<ErrorResponse>(500);

// Generate text endpoint (keeping for backward compatibility)
app.MapPost("/api/generate", GenerateEndpoint)
    .WithName("GenerateText")
    .WithSummary("Generate text using AI providers")
    .WithDescription("Generate text using various AI providers like OpenAI, Claude, Google, etc.")
    .WithOpenApi(operation =>
    {
        operation.Security = new List<Microsoft.OpenApi.Models.OpenApiSecurityRequirement>
        {
            new()
            {
                {
                    new Microsoft.OpenApi.Models.OpenApiSecurityScheme
                    {
                        Reference = new Microsoft.OpenApi.Models.OpenApiReference
                        {
                            Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                            Id = "ApiKey"
                        }
                    },
                    Array.Empty<string>()
                }
            }
        };
        return operation;
    })
    .Produces<GenerateResponse>(200)
    .Produces<ErrorResponse>(400)
    .Produces<ErrorResponse>(401)
    .Produces<ErrorResponse>(500);

app.Run();

/// <summary>
/// Get health status endpoint delegate
/// </summary>
static async Task<IResult> GetHealthStatusEndpoint(
    [FromServices] IMediator mediator,
    [FromQuery] bool includeProviderStatus = true,
    [FromQuery] bool includeSystemInfo = false,
    CancellationToken cancellationToken = default)
{
    var query = new GetHealthStatusQuery
    {
        IncludeProviderStatus = includeProviderStatus,
        IncludeSystemInfo = includeSystemInfo
    };

    var response = await mediator.Send(query, cancellationToken);
    return Results.Ok(response);
}

/// <summary>
/// Get providers endpoint delegate
/// </summary>
static async Task<IResult> GetProvidersEndpoint(
    [FromServices] IMediator mediator,
    [FromQuery] bool enabledOnly = false,
    [FromQuery] bool checkAvailability = true,
    CancellationToken cancellationToken = default)
{
    var query = new GetProvidersQuery
    {
        EnabledOnly = enabledOnly,
        CheckAvailability = checkAvailability
    };

    var response = await mediator.Send(query, cancellationToken);

    if (!response.Success)
    {
        return Results.BadRequest(new { Message = response.ErrorMessage });
    }

    return Results.Ok(response);
}

/// <summary>
/// Get provider models endpoint delegate
/// </summary>
static async Task<IResult> GetProviderModelsEndpoint(
    [FromRoute] string provider,
    [FromServices] IMediator mediator,
    [FromQuery] bool enabledOnly = true,
    [FromQuery] bool includeCapabilities = false,
    CancellationToken cancellationToken = default)
{
    if (string.IsNullOrWhiteSpace(provider))
    {
        return Results.BadRequest(new { Message = "Provider parameter is required" });
    }

    var query = new GetProviderModelsQuery
    {
        Provider = provider,
        EnabledOnly = enabledOnly,
        IncludeCapabilities = includeCapabilities
    };

    var response = await mediator.Send(query, cancellationToken);

    if (!response.Success)
    {
        return Results.NotFound(new { Message = response.ErrorMessage });
    }

    return Results.Ok(response);
}

/// <summary>
/// Chat completion endpoint delegate
/// </summary>
static async Task<IResult> ChatCompletionEndpoint(
    [FromBody] ChatRequest request,
    [FromServices] IMediator mediator,
    [FromServices] IValidator<ChatRequest> validator,
    CancellationToken cancellationToken)
{
    // Validate the request
    var validationResult = await validator.ValidateAsync(request, cancellationToken);
    if (!validationResult.IsValid)
    {
        var errors = validationResult.Errors.Select(e => new { Field = e.PropertyName, Error = e.ErrorMessage });
        return Results.BadRequest(new { Message = "Validation failed", Errors = errors });
    }

    var command = new ChatCompletionCommand
    {
        Messages = request.Messages,
        MaxTokens = request.MaxTokens,
        Temperature = request.Temperature,
        TopP = request.TopP,
        Provider = request.Provider,
        Model = request.Model,
        Stream = request.Stream,
        ConversationId = request.ConversationId
    };

    var response = await mediator.Send(command, cancellationToken);

    if (!response.Success)
    {
        return Results.BadRequest(new { Message = response.ErrorMessage });
    }

    return Results.Ok(response);
}

/// <summary>
/// Text completion endpoint delegate
/// </summary>
static async Task<IResult> TextCompletionEndpoint(
    [FromBody] CompletionRequest request,
    [FromServices] IMediator mediator,
    [FromServices] IValidator<CompletionRequest> validator,
    CancellationToken cancellationToken)
{
    // Validate the request
    var validationResult = await validator.ValidateAsync(request, cancellationToken);
    if (!validationResult.IsValid)
    {
        var errors = validationResult.Errors.Select(e => new { Field = e.PropertyName, Error = e.ErrorMessage });
        return Results.BadRequest(new { Message = "Validation failed", Errors = errors });
    }

    var command = new TextCompletionCommand
    {
        Prompt = request.Prompt,
        MaxTokens = request.MaxTokens,
        Temperature = request.Temperature,
        TopP = request.TopP,
        Provider = request.Provider,
        Model = request.Model,
        NumberOfCompletions = request.NumberOfCompletions,
        StopSequences = request.StopSequences,
        Echo = request.Echo
    };

    var response = await mediator.Send(command, cancellationToken);

    if (!response.Success)
    {
        return Results.BadRequest(new { Message = response.ErrorMessage });
    }

    return Results.Ok(response);
}

/// <summary>
/// Generate text endpoint delegate (backward compatibility)
/// </summary>
static async Task<IResult> GenerateEndpoint(
    [FromBody] GenerateRequest request,
    [FromServices] IMediator mediator,
    [FromServices] IValidator<GenerateRequest> validator,
    CancellationToken cancellationToken)
{
    // Validate the request
    var validationResult = await validator.ValidateAsync(request, cancellationToken);
    if (!validationResult.IsValid)
    {
        var errors = validationResult.Errors.Select(e => new { Field = e.PropertyName, Error = e.ErrorMessage });
        return Results.BadRequest(new { Message = "Validation failed", Errors = errors });
    }

    var command = new GenerateTextCommand
    {
        Prompt = request.Prompt,
        MaxTokens = request.MaxTokens,
        Temperature = request.Temperature,
        TopP = request.TopP,
        Provider = request.Provider,
        Model = request.Model,
        SystemMessage = request.SystemMessage,
        Stream = request.Stream
    };

    var response = await mediator.Send(command, cancellationToken);

    if (!response.Success)
    {
        return Results.BadRequest(new { Message = response.ErrorMessage });
    }

    return Results.Ok(response);
}