using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using Ai.Integrate.Infrastructure.AIProvides;
using Ai.Integrate.Infrastructure.Config;
using Ai.Integrate.Shared.Extensions;
using Ai.Integrate.Shared.Middleware;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Scalar.AspNetCore;
using Serilog;
using System.ComponentModel.DataAnnotations;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/ai-integration-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddOpenApi();

// Add configuration settings
builder.Services.AddConfigurationSettings(builder.Configuration);

// Add application services
builder.Services.AddApplicationServices();

// Add HTTP clients with Polly policies
builder.Services.AddHttpClientsWithPolly(builder.Configuration);

// Add RestSharp clients
builder.Services.AddRestSharpClients(builder.Configuration);

// Register AI providers
builder.Services.AddScoped<IAIService, OpenAIProvider>();
// Add other providers when implemented:
// builder.Services.AddScoped<IAIService, ClaudeProvider>();
// builder.Services.AddScoped<IAIService, GoogleProvider>();

// Add FluentValidation
builder.Services.AddValidatorsFromAssemblyContaining<GenerateRequest>();

// Add health checks
builder.Services.AddHealthChecks();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
app.UseMiddleware<GlobalExceptionMiddleware>();

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference(options =>
    {
        options.Title = "AI Integration Service API";
        options.Theme = ScalarTheme.BluePlanet;
        options.ShowSidebar = true;
        options.Authentication = new ScalarAuthenticationOptions
        {
            PreferredSecurityScheme = "ApiKey",
            ApiKey = new ApiKeyOptions
            {
                Token = "your-api-key-here"
            }
        };
    });
}

app.UseCors();
app.UseHttpsRedirection();

// Add API key authentication middleware
app.UseMiddleware<ApiKeyAuthMiddleware>();

// Add health check endpoint
app.MapHealthChecks("/health");

// Generate text endpoint
app.MapPost("/api/generate", GenerateEndpoint)
    .WithName("GenerateText")
    .WithSummary("Generate text using AI providers")
    .WithDescription("Generate text using various AI providers like OpenAI, Claude, Google, etc.")
    .WithOpenApi(operation =>
    {
        operation.Security = new List<Microsoft.OpenApi.Models.OpenApiSecurityRequirement>
        {
            new()
            {
                {
                    new Microsoft.OpenApi.Models.OpenApiSecurityScheme
                    {
                        Reference = new Microsoft.OpenApi.Models.OpenApiReference
                        {
                            Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                            Id = "ApiKey"
                        }
                    },
                    Array.Empty<string>()
                }
            }
        };
        return operation;
    })
    .Produces<GenerateResponse>(200)
    .Produces<ErrorResponse>(400)
    .Produces<ErrorResponse>(401)
    .Produces<ErrorResponse>(500);

app.Run();

/// <summary>
/// Generate text endpoint delegate
/// </summary>
static async Task<IResult> GenerateEndpoint(
    [FromBody] GenerateRequest request,
    [FromServices] IGenerateTextUseCase generateTextUseCase,
    [FromServices] IValidator<GenerateRequest> validator,
    CancellationToken cancellationToken)
{
    // Validate the request
    var validationResult = await validator.ValidateAsync(request, cancellationToken);
    if (!validationResult.IsValid)
    {
        var errors = validationResult.Errors.Select(e => new { Field = e.PropertyName, Error = e.ErrorMessage });
        return Results.BadRequest(new { Message = "Validation failed", Errors = errors });
    }

    // Execute the use case
    var response = await generateTextUseCase.ExecuteAsync(request, cancellationToken);

    if (!response.Success)
    {
        return Results.BadRequest(new { Message = response.ErrorMessage });
    }

    return Results.Ok(response);
}