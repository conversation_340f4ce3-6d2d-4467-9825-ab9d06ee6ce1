using Ai.Integrate.Application.Dtos;
using Ai.Integrate.Application.Interfaces;
using Ai.Integrate.Application.Queries;
using Ai.Integrate.Infrastructure.Config;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Ai.Integrate.Application.Handlers;

/// <summary>
/// Handler for GetProvidersQuery
/// </summary>
public class GetProvidersQueryHandler : IRequestHandler<GetProvidersQuery, ProvidersResponse>
{
    private readonly IEnumerable<IAIService> _aiServices;
    private readonly AISettings _settings;
    private readonly ILogger<GetProvidersQueryHandler> _logger;

    public GetProvidersQueryHandler(
        IEnumerable<IAIService> aiServices,
        IOptions<AISettings> settings,
        ILogger<GetProvidersQueryHandler> logger)
    {
        _aiServices = aiServices ?? throw new ArgumentNullException(nameof(aiServices));
        _settings = settings.Value ?? throw new ArgumentNullException(nameof(settings));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ProvidersResponse> Handle(GetProvidersQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling GetProvidersQuery");

        try
        {
            var providers = new List<ProviderInfo>();

            // Get all configured providers from settings
            var providerConfigs = new[]
            {
                new { Name = "OpenAI", Config = _settings.OpenAI, DisplayName = "OpenAI", Description = "OpenAI GPT models" },
                new { Name = "Claude", Config = _settings.Claude, DisplayName = "Anthropic Claude", Description = "Anthropic Claude models" },
                new { Name = "Google", Config = _settings.Google, DisplayName = "Google AI", Description = "Google Gemini models" },
                new { Name = "OpenRouter", Config = _settings.OpenRouter, DisplayName = "OpenRouter", Description = "OpenRouter API gateway" },
                new { Name = "Llama", Config = _settings.Llama, DisplayName = "Llama", Description = "Local Llama models" },
                new { Name = "LangChain", Config = _settings.LangChain, DisplayName = "LangChain", Description = "LangChain integration" }
            };

            foreach (var providerConfig in providerConfigs)
            {
                // Skip disabled providers if requested
                if (request.EnabledOnly && !providerConfig.Config.Enabled)
                    continue;

                var aiService = _aiServices.FirstOrDefault(s => 
                    string.Equals(s.ProviderName, providerConfig.Name, StringComparison.OrdinalIgnoreCase));

                bool isAvailable = false;
                string status = "Not Configured";
                bool isConfigured = false;

                // Check if provider is configured
                if (providerConfig.Name == "OpenAI")
                {
                    isConfigured = !string.IsNullOrEmpty(_settings.OpenAI.ApiKey);
                }
                else if (providerConfig.Name == "Claude")
                {
                    isConfigured = !string.IsNullOrEmpty(_settings.Claude.ApiKey);
                }
                else if (providerConfig.Name == "Google")
                {
                    isConfigured = !string.IsNullOrEmpty(_settings.Google.ApiKey);
                }
                else if (providerConfig.Name == "OpenRouter")
                {
                    isConfigured = !string.IsNullOrEmpty(_settings.OpenRouter.ApiKey);
                }
                else if (providerConfig.Name == "Llama")
                {
                    isConfigured = !string.IsNullOrEmpty(_settings.Llama.BaseUrl);
                }
                else if (providerConfig.Name == "LangChain")
                {
                    isConfigured = !string.IsNullOrEmpty(_settings.LangChain.BaseUrl);
                }

                if (isConfigured && providerConfig.Config.Enabled)
                {
                    status = "Configured";
                    
                    // Check availability if requested and service is available
                    if (request.CheckAvailability && aiService != null)
                    {
                        try
                        {
                            isAvailable = await aiService.IsAvailableAsync(cancellationToken);
                            status = isAvailable ? "Available" : "Unavailable";
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to check availability for provider {Provider}", providerConfig.Name);
                            status = "Error";
                        }
                    }
                }
                else if (!providerConfig.Config.Enabled)
                {
                    status = "Disabled";
                }

                var capabilities = new List<string> { "text-generation" };
                if (providerConfig.Name == "OpenAI" || providerConfig.Name == "Claude")
                {
                    capabilities.AddRange(new[] { "chat", "completion", "streaming" });
                }

                providers.Add(new ProviderInfo
                {
                    Name = providerConfig.Name,
                    DisplayName = providerConfig.DisplayName,
                    Description = providerConfig.Description,
                    Enabled = providerConfig.Config.Enabled,
                    Available = isAvailable,
                    Status = status,
                    Capabilities = capabilities,
                    DefaultModel = providerConfig.Config.DefaultModel,
                    ModelCount = GetModelCount(providerConfig.Name),
                    Configured = isConfigured,
                    LastChecked = DateTime.UtcNow
                });
            }

            var response = new ProvidersResponse
            {
                Providers = providers,
                DefaultProvider = _settings.DefaultProvider,
                Success = true
            };

            _logger.LogInformation("Successfully retrieved {Count} providers", providers.Count);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting providers");
            return new ProvidersResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred while getting providers: {ex.Message}"
            };
        }
    }

    private static int GetModelCount(string providerName)
    {
        // This would typically come from a database or external API
        return providerName switch
        {
            "OpenAI" => 15,
            "Claude" => 3,
            "Google" => 5,
            "OpenRouter" => 50,
            "Llama" => 10,
            "LangChain" => 1,
            _ => 0
        };
    }
}
